package com.asmtunis.procaisseinventory.auth

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CutCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.LoginRoute
import com.asmtunis.procaisseinventory.core.navigation.SubscribtionRoute
import com.asmtunis.procaisseinventory.core.navigation.WaitingLicenceActivationRoute
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json


@Composable
fun NoLicenseScreen(
    navigate: (route: Any) -> Unit,
    dataViewModel: DataViewModel,
    authViewModel: AuthViewModel,
    networkViewModel: NetworkViewModel
) {

    val context = LocalContext.current
    val isConnected = networkViewModel.isConnected
    Scaffold { padding ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_asm),
                contentDescription = "",
                modifier = Modifier
                    .padding(6.dp)
                    // .border(2.dp, Color.Black, CutCornerShape(12.dp))
                    .background(Color.DarkGray, CutCornerShape(6.dp))
                    .padding(6.dp)
            )
            Text(
                text = DEVICE_ID
            )
            Spacer(Modifier.height(12.dp))

            LottieAnim(lotti = R.raw.licence_expired, size = 250.dp)
            Spacer(Modifier.height(12.dp))
            Row(
                modifier =  Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                OutlinedButton(
                    enabled = isConnected,
                    onClick = {
                        navigate(SubscribtionRoute)
                    },
                    shape = MaterialTheme.shapes.medium
                ) {
                    if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 30.dp)

                    Text(text = stringResource(R.string.demande_licence))
                }


                OutlinedButton(
                    enabled = isConnected,
                    onClick = {
                        authViewModel.setBaseConfigTable()
                        dataViewModel.saveSelectedBaseConfig(Json.encodeToString(authViewModel.demoBaseConfig.first()))
                        authViewModel.onSelectedIndexBaseConfigValue(authViewModel.demoBaseConfig.first())


                            dataViewModel.saveIsProInventoryLicenseSelected(true)
                            authViewModel.onSelectedProInventoryLicense(true)


                            dataViewModel.saveIsProcaisseLicenseSelected(true)
                            authViewModel.onSelectedProCaisseLicense(true, from = "2")

                        navigate(LoginRoute)
                    },
                    shape = MaterialTheme.shapes.medium
                ) {
                    if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 30.dp)

                    Text(text = stringResource(R.string.demo))
                }



            }

            Spacer(Modifier.height(12.dp))
            if(dataViewModel.getProInventorySubscribtionSent() ||dataViewModel.getProcaisseSubscribtionSent())
                OutlinedButton(
                    enabled = isConnected,
                    onClick = {
                        //TODO ADD BASE CONFIG
                        navigate(WaitingLicenceActivationRoute)
                    },
                    modifier = Modifier
                        .wrapContentWidth()
                        .height(55.dp),
                    shape = MaterialTheme.shapes.medium
                ) {
                    if (!isConnected) LottieAnim(lotti = R.raw.no_connection, size = 30.dp)

                    Text(text = "Vérifier License: " + if(dataViewModel.getProInventorySubscribtionSent() &&dataViewModel.getProcaisseSubscribtionSent()) "ProCaisse et ProInventory"
                    else if(dataViewModel.getProInventorySubscribtionSent()) PRO_INVENTORY_AUTHORIZATION_TYPE_MENU else PRO_CAISSE)
                }
        }
    }

}
