package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.remote.di

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.remote.api.CheckLicenceApi
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.check_license.remote.api.CheckLicenceApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object CheckLicenceRemoteModule {

    @Provides
    @Singleton
    fun provideCheckLicenceApi(client: HttpClient): CheckLicenceApi = CheckLicenceApiImpl(client)
    
}