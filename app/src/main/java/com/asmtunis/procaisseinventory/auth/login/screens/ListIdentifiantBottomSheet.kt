package com.asmtunis.procaisseinventory.auth.login.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.listIdentifiant
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ListIdentifiantBottomSheet(onDismissRequest: () -> Unit) {
    val sheetState = rememberModalBottomSheetState()
    val scope = rememberCoroutineScope()
    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = {
            scope.launch {
                sheetState.hide()
            }
            onDismissRequest()
        },
    ) {
        Column(
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier.fillMaxWidth(),
        ) {
            val listState = rememberLazyListState()

            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier =
                Modifier
                    .heightIn(40.dp)
                    .fillMaxWidth()
                    //  .background(color = MaterialTheme.colorScheme.scrim)
                    .clickable {
                    },
            ) {
                Text(
                    text = stringResource(id = R.string.user),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(0.4f),
                )
                Text(
                    text = stringResource(id = R.string.username_title),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(0.3f),
                )

                Text(
                    text = stringResource(id = R.string.password_title),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(0.3f),
                )
            }
            HorizontalDivider(color = MaterialTheme.colorScheme.outline)

            LazyColumn(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                state = listState,
            ) {
                items(
                    count = listIdentifiant.size,
                    key = {
                        listIdentifiant[it].type
                    },
                ) { index ->

                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        modifier =
                        Modifier
                            .padding(start = 3.dp, end = 3.dp)
                            .heightIn(40.dp, 150.dp)
                            .fillMaxWidth(),
                    ) {
                        Text(
                            text = listIdentifiant[index].type,
                            textAlign = TextAlign.Center,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            modifier = Modifier.fillMaxWidth(0.4f),
                        )

                        Text(
                            text = listIdentifiant[index].identifiant,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth(0.3f),
                        )

                        Text(
                            text = listIdentifiant[index].password,
                            textAlign = TextAlign.Center,
                            fontSize = MaterialTheme.typography.bodySmall.fontSize,
                            modifier = Modifier.fillMaxWidth(0.3f),
                        )
                    }

                    HorizontalDivider(color = MaterialTheme.colorScheme.outline)
                }
            }
        }
    }
}
