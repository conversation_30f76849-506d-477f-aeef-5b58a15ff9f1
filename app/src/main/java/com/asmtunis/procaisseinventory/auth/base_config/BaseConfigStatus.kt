package com.asmtunis.procaisseinventory.auth.base_config

import com.asmtunis.procaisseinventory.core.Globals.PRO_CAISSE_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU





enum class HeaderStatus(var header: String) {
    PROCAISSE(PRO_CAISSE_AUTHORIZATION_TYPE_MENU),
    INVENTORY(PRO_INVENTORY_AUTHORIZATION_TYPE_MENU),
    PROCAISSE_AND_INVENTORY("$PRO_CAISSE_AUTHORIZATION_TYPE_MENU;$PRO_INVENTORY_AUTHORIZATION_TYPE_MENU"),
    EMPTY(""),


}