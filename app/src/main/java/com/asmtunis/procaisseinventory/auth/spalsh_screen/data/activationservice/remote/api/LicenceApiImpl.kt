package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.remote.api

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.core.Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow

class LicenceApiImpl(private val client: HttpClient) : LicenceApi {



    override suspend fun getLicenceInventory(deviceId: String, product: String): Flow<DataResult<Licence>> = flow {


  val headers = mapOf("Application-name" to PRO_INVENTORY_AUTHORIZATION_TYPE_MENU)

        val  formsData = mapOf(
            "iddevice" to deviceId,
            "produit" to product
        )
        val result = executePostApiCall<Licence>(
            client = client,
            baseUrl = Urls.CHECK_LICENCE,
            headers = headers,
            formsData = formsData,
            endpoint = Urls.CHECK_LICENCE,

        )
        emitAll(result)
    }
}
