package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.repository

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.dao.LicenceDAO
import kotlinx.coroutines.flow.Flow


class LicenceLocalRepositoryImpl(
        private val licenceDAO: LicenceDAO
    ) : LicenceLocalRepository {
    override fun upsert(value: Licence) = licenceDAO.upsert(value)

    override fun upsertAll(value: List<Licence>) = licenceDAO.upsertAll(value)

    override fun deletebyProduct(value: String) = licenceDAO.deleteByProduct(value)

    override fun deleteAll() = licenceDAO.deleteAll()

    override fun getAll(): Flow<List<Licence>> = licenceDAO.getAll()

    override fun getByProduct(product: String): Flow<Licence?> = licenceDAO.getByProduct(product)
}