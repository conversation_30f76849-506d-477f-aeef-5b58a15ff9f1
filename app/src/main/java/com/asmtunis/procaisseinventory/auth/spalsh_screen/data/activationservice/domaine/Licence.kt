package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.LICENCE_TABLE, primaryKeys = ["id"])
@Serializable
data class Licence(
    @ColumnInfo(name = "id")
    var id: Long = -1L,
    @ColumnInfo(name = "activat")
    var activat: String = "",
    @ColumnInfo(name = "bon_sortie")
    var bonSortie: String? = "",
    @ColumnInfo(name = "code_activation")
    var codeActivation: String? = "",
    @ColumnInfo(name = "date_activation")
    var dateActivation: String? = "",
    @ColumnInfo(name = "date_conx")
    var dateConx: String? = "",
    @ColumnInfo(name = "datef")
    var datef: String? = "",
    @ColumnInfo(name = "dater")
    var dater: Double? = 0.0,
    @ColumnInfo(name = "demo")
    var demo: String? = "",
    @ColumnInfo(name = "device")
    var device: String? = "",
    @ColumnInfo(name = "email")
    var email: String? = "",
    @ColumnInfo(name = "etablissement")
    var etablissement: String? = "",
    @ColumnInfo(name = "etat")
    var etat: String? = "",
    @ColumnInfo(name = "heure_activation")
    var heureActivation: String? = "",
    @ColumnInfo(name = "id_CPU")
    var idCPU: String? = "",
    @ColumnInfo(name = "id_client")
    var idClient: String? = "",
    @ColumnInfo(name = "id_device")
    var id_device: String? = "",
    @ColumnInfo(name = "id_integrateur")
    var id_integrateur: String? = "",
    @ColumnInfo(name = "id_user")
    var id_user: String? = "",
    @ColumnInfo(name = "nom_prenom")
    var nom_prenom: String? = "",
    @ColumnInfo(name = "offre")
    var offre: String? = "",
    @ColumnInfo(name = "pays")
    var pays: String? = "",
    @ColumnInfo(name = "port")
    var port: Int = -1,
    @ColumnInfo(name = "prix")
    var prix: String? = "",
    @ColumnInfo(name = "produit")
    var produit: String? = "",
    @ColumnInfo(name = "telephone")
    var telephone: String = "",
    @ColumnInfo(name = "type")
    var type: String? = "",
    @ColumnInfo(name = "url")
    var url: String? = "",
    @ColumnInfo(name = "user")
    var user: String? = "",
    @ColumnInfo(name = "version")
    var version: String? = ""

)
