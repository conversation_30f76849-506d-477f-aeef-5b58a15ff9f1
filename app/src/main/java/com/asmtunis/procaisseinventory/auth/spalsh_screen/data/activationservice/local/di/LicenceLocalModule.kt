package com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.di

import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.dao.LicenceDAO
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.repository.LicenceLocalRepository
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.local.repository.LicenceLocalRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class LicenceLocalModule {

    @Provides
    @Singleton
    fun provideLicenceDao(
        licenceProCaisseDataBase: ProCaisseDataBase
    ) = licenceProCaisseDataBase.licenceDao()

    @Provides
    @Singleton
    @Named("Licence")
    fun provideLicenceRepository(
        licenceDAO: LicenceDAO
    ): LicenceLocalRepository = LicenceLocalRepositoryImpl(
        licenceDAO = licenceDAO

    )

}