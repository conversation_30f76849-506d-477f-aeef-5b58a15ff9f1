package com.asmtunis.procaisseinventory.data.prefixe.local.di

import com.asmtunis.procaisseinventory.data.prefixe.local.dao.PrefixeDAO
import com.asmtunis.procaisseinventory.data.prefixe.local.repository.PrefixeLocalRepository
import com.asmtunis.procaisseinventory.data.prefixe.local.repository.PrefixeLocalRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class PrefixModule {

    @Provides
    @Singleton
    fun providePrefixDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.prefixeDAO()

    @Provides
    @Singleton
    @Named("Prefix")
    fun providePrefixRepository(
        prefixeDAO: PrefixeDAO
    ): PrefixeLocalRepository = PrefixeLocalRepositoryImpl(
        prefixDAO = prefixeDAO

    )


}

