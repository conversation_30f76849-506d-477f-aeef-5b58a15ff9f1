package com.asmtunis.procaisseinventory.data.cheque_caisse.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import kotlinx.coroutines.flow.Flow


interface ChequeCaisseApi {



    suspend fun getChequeCaisseByReglements(baseConfig: String): Flow<DataResult<List<List<ChequeCaisse>>>>
    suspend fun getChequeCaisseByReglement(baseConfig: String): Flow<DataResult<List<ChequeCaisse>>>

}