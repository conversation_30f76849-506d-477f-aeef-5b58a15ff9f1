package com.asmtunis.procaisseinventory.data.ticket_resto.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


//@Entity(tableName = ProCaisseConstants.TICKET_RESTO_TABLE,primaryKeys = ["TRAIT_Num", "TRAIT_Ordre", "TRAIT_Reglement", "TRAIT_IdSession", "TRAIT_Exercice"])
@Entity(tableName = ProCaisseConstants.TICKET_RESTO_TABLE)
@Serializable
data class TraiteCaisse  (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,

    @ColumnInfo(name = "TRAIT_Num")
    @SerialName("TRAIT_Num")
    var tRAITNum: String = "",

    @ColumnInfo(name = "TRAIT_Ordre")
    @SerialName("TRAIT_Ordre")
    var tRAITOrdre : Int= 0,

    @ColumnInfo(name = "TRAIT_Num_M")
    @SerialName("TRAIT_Num_M")
    var tRAITNUM_M: String? = "",

    @ColumnInfo(name = "TRAIT_Reglement")
    @SerialName("TRAIT_Reglement")
    
    var tRAITReglement: String = "",

    @ColumnInfo(name = "TRAIT_IdSession")
    @SerialName("TRAIT_IdSession")
    
    var tRAITIdSession: String = "",

    @ColumnInfo(name = "TRAIT_Exercice")
    @SerialName("TRAIT_Exercice")
    
    var tRAITExercice: String = "",

    @ColumnInfo(name = "TRAIT_Echeance")
    @SerialName("TRAIT_Echeance")
    
    var tRAITEcheance: String? = "",

    @ColumnInfo(name = "TRAIT_Client")
    @SerialName("TRAIT_Client")
    
    var tRAITClient: String? = "",

    @ColumnInfo(name = "TRAIT_Montant")
    @SerialName("TRAIT_Montant")
    
    var tRAITMontant : Double = 0.0,

    @ColumnInfo(name = "TRAIT_type")
    @SerialName("TRAIT_type")
    
    var tRAITType: String? = "",

    @ColumnInfo(name = "TRAIT_Compte_local")
    @SerialName("TRAIT_Compte_local")
    
    var tRAITCompteLocal: String = "",




): BaseModel() {
    @Transient
    @Ignore
    var taux : Double = 0.0


    @Transient
    @Ignore
    var montantInitial : Double = 0.0

    @Transient
    @Ignore
    var nbrTicket : Int = 0

}


