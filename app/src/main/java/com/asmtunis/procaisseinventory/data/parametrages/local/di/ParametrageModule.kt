package com.asmtunis.procaisseinventory.data.parametrages.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.parametrages.local.dao.ParametrageDAO
import com.asmtunis.procaisseinventory.data.parametrages.local.repository.ParametrageLocalRepository
import com.asmtunis.procaisseinventory.data.parametrages.local.repository.ParametrageLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class ParametrageModule {

    @Provides
    @Singleton
    fun provideParametrageDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.parametragesDAO()

    @Provides
    @Singleton
    @Named("Parametrage")
    fun provideParametrageRepository(
        parametrageDAO: ParametrageDAO
    ): ParametrageLocalRepository = ParametrageLocalRepositoryImpl(
        parametrageDAO = parametrageDAO

    )


}