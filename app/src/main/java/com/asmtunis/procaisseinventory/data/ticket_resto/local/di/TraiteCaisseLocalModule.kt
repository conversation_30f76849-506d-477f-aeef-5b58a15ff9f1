package com.asmtunis.procaisseinventory.data.ticket_resto.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.ticket_resto.local.dao.TraiteCaisseDAO
import com.asmtunis.procaisseinventory.data.ticket_resto.local.repository.TraiteCaisseLocalRepository
import com.asmtunis.procaisseinventory.data.ticket_resto.local.repository.TraiteCaisseLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class TraiteCaisseLocalModule {

    @Provides
    @Singleton
    fun provideTraiteCaisseDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.traiteCaisseDAO()

    @Provides
    @Singleton
    @Named("TraiteCaisse")
    fun provideTraiteCaisseRepository(
        traiteCaisseDAO: TraiteCaisseDAO
    ): TraiteCaisseLocalRepository = TraiteCaisseLocalRepositoryImpl(
        traiteCaisseDAO = traiteCaisseDAO

    )

}