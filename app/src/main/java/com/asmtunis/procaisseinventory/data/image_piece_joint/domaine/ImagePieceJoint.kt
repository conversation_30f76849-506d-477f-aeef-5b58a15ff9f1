package com.asmtunis.procaisseinventory.data.image_piece_joint.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.PIECE_JOINTS_TABLE)
@Serializable
data class ImagePieceJoint(

    /*  @PrimaryKey(autoGenerate = true)
  @Transient
  var id: Long = 0,*/


    @PrimaryKey
    @SerialName("Code_IMG")
    @ColumnInfo(name = "Code_IMG")
    var codeIMG: String = "",


    @SerialName("Code_Mob")
    @ColumnInfo(name = "Code_Mob")
    var codeMob: String = "",
    @SerialName("DateOp")
    @ColumnInfo(name = "DateOp")
    var dateOp: String = "",
    @SerialName("Image")
    @ColumnInfo(name = "Image")
    var image: String? = "",






   @SerialName("imgurl")
    @ColumnInfo(name = "imgurl")
    var imgUrl: String? ="",



    @ColumnInfo(name = "devNum")
    @Transient
    var devNum: String = "",








    @SerialName("Type_VC")
    @ColumnInfo(name = "Type_VC")
    var typeVC: String = "",



    @SerialName("Code_TypeVC")
    @ColumnInfo(name = "Code_TypeVC")
    var codeTypeVC: String = "",





    @SerialName("CodeUser")
    @ColumnInfo(name = "CodeUser")
    var codeUser: Int = 0,

    @ColumnInfo(name = "Chemin_Img")
    @SerialName("Chemin_Img")
    var cheminImg: String? = "",


    @ColumnInfo(name = "VCNumSerie")
    @SerialName("VCNumSerie")
    var vcNumSerie: String? = "",






) : BaseModel()