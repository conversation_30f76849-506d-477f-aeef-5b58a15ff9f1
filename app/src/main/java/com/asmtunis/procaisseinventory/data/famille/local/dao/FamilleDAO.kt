package com.asmtunis.procaisseinventory.data.famille.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.FAMILLE_TABLE
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import kotlinx.coroutines.flow.Flow


@Dao
interface FamilleDAO {
    @get:Query("SELECT * FROM $FAMILLE_TABLE")
    val all: Flow<List<Famille>>

    @Query("SELECT * FROM $FAMILLE_TABLE WHERE isSync=0 and  (Status=:status) ")
    fun getByStatus(status: String): Flow<List<Famille>>

    @Query("SELECT * FROM $FAMILLE_TABLE WHERE   (Status=:status) ")
    fun getByStatusForced(status: String): Flow<List<Famille>>

    @Query("SELECT COUNT(*) FROM $FAMILLE_TABLE where  isSync=0 and  (Status='INSERTED'  or Status='UPDATED' or Status='DELETED')")
    fun count(): Flow<Int>

    @Query("SELECT ifnull(MAX(cast(substr(FAM_Code,length(:prefix) + 1 ,length('FAM_Code'))as integer)),0)+1 FROM Famille WHERE substr(FAM_Code, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @get:Query("SELECT * FROM $FAMILLE_TABLE LIMIT 1")
    val one: Flow<Famille>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Famille)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Famille>)

    @Query("UPDATE $FAMILLE_TABLE SET isSync=1 , Status='SELECTED'")
    fun updateStatus()

    @Query("UPDATE $FAMILLE_TABLE SET isSync = 1, Status= 'SELECTED' where FAM_Code = :famCode")
    fun updateSyncFamille(famCode : String)
    @Query("DELETE FROM $FAMILLE_TABLE")
    fun deleteAll()

    @get:Query("SELECT * FROM $FAMILLE_TABLE WHERE isSync = 0  and  (Status='INSERTED'  or Status='UPDATED')")
    val nonSync: Flow<List<Famille>>

    @get:Query("SELECT FAM_Lib FROM $FAMILLE_TABLE WHERE FAM_Lib IS  NOT NULL")
    val list: Flow<List<String>>
}
