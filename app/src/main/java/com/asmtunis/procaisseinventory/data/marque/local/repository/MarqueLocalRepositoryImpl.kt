package com.asmtunis.procaisseinventory.data.marque.local.repository


import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.marque.local.dao.MarqueDAO
import kotlinx.coroutines.flow.Flow


class MarqueLocalRepositoryImpl(
        private val marqueDAO: MarqueDAO
    ) : MarqueLocalRepository {
    override fun upsertAll(value: List<Marque>) =
        marqueDAO.insertAll(value)
    override fun upsert(value: Marque) =
        marqueDAO.insert(value)
    override fun deleteAll() =
        marqueDAO.deleteAll()

    override fun getNewCode(prefix: String): Flow<String> =
        marqueDAO.getNewCode(prefix)

    override fun getNotSync(): Flow<List<Marque>>  =
        marqueDAO.nonSync

    override fun updateSyncMarque(marCode: String)  =
        marqueDAO.updateSyncMarque(marCode)

    override fun getAll(): Flow<List<Marque>?> =
        marqueDAO.all
}