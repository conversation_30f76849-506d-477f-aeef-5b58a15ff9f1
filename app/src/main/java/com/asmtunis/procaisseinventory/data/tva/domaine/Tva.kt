package com.asmtunis.procaisseinventory.data.tva.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.TVA_TABLE
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = TVA_TABLE)
@Serializable
  data  class Tva (
    @PrimaryKey
    @ColumnInfo(name = "TVA_Code")
    @SerialName("TVA_Code")
    var tVACode: String = "",

    @ColumnInfo(name = "TVA_User")
    @SerialName("TVA_User")
    var tVAUser: String? = "",

    @ColumnInfo(name = "TVA_Station")
    @SerialName("TVA_Station")
    var tVAStation: String? = "",

    @ColumnInfo(name = "TVA_export")
    @SerialName("TVA_export")
    var tVAExport: String? = "",

    @ColumnInfo(name = "TVA_DDm")
    @SerialName("TVA_DDm")
    var tVADDm: String? = "",

    @ColumnInfo(name = "TVA_deductible")
    @SerialName("TVA_deductible")
    var tVADeductible: String? = "",

    @ColumnInfo(name = "TVA_Collecter")
    @SerialName("TVA_Collecter")
    var tVACollecter: String? = "",

    @ColumnInfo(name = "code_comp_collecte")
    @SerialName("code_comp_collecte")
    var codeCompCollecte: String? = "",

    @ColumnInfo(name = "code_comp_deductible")
    @SerialName("code_comp_deductible")
    var codeCompDeductible: String? = ""


    )
