package com.asmtunis.procaisseinventory.data.image_piece_joint.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.image_piece_joint.local.dao.InventairePieceJointDAO
import com.asmtunis.procaisseinventory.data.image_piece_joint.local.repository.InventairePieceJointLocalRepository
import com.asmtunis.procaisseinventory.data.image_piece_joint.local.repository.InventairePieceJointLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class InventairePieceJointLocalModule {

    @Provides
    @Singleton
    fun provideInventairePieceJointDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.inventairePieceJointDAO()

    @Provides
    @Singleton
    @Named("InventairePieceJoint")
    fun provideInventairePieceJointRepository(
        inventairePieceJointDAO: InventairePieceJointDAO
    ): InventairePieceJointLocalRepository = InventairePieceJointLocalRepositoryImpl(
        inventairePieceJointDAO = inventairePieceJointDAO
    )


}