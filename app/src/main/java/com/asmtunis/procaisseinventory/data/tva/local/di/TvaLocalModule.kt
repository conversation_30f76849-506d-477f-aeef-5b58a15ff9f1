package com.asmtunis.procaisseinventory.data.tva.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.tva.local.dao.TvaDAO
import com.asmtunis.procaisseinventory.data.tva.local.repository.TvaLocalRepository
import com.asmtunis.procaisseinventory.data.tva.local.repository.TvaLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class TvaLocalModule {

    @Provides
    @Singleton
    fun provideTvaDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.tvaDAO()

    @Provides
    @Singleton
    @Named("Tva")
    fun provideTvaRepository(
        tvaDAO: TvaDAO
    ): TvaLocalRepository = TvaLocalRepositoryImpl(
        tvaDAO = tvaDAO
    )

}