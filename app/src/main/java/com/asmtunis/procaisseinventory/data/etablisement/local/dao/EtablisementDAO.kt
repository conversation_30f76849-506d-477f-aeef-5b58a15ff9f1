package com.asmtunis.procaisseinventory.data.etablisement.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.ETABLISSEMENT_TABLE
import com.asmtunis.procaisseinventory.data.etablisement.domaine.Etablisement
import kotlinx.coroutines.flow.Flow


@Dao
interface EtablisementDAO {
    @get:Query("SELECT * FROM $ETABLISSEMENT_TABLE")
    val all: Flow<List<Etablisement>?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(item: Etablisement)

    @Query("DELETE FROM $ETABLISSEMENT_TABLE")
    fun deleteAll()
}