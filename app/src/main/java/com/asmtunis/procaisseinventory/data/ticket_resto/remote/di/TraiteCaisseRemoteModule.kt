package com.asmtunis.procaisseinventory.data.ticket_resto.remote.di

import com.asmtunis.procaisseinventory.data.ticket_resto.remote.api.TraiteCaisseApi
import com.asmtunis.procaisseinventory.data.ticket_resto.remote.api.TraiteCaisseApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object TraiteCaisseRemoteModule {


    @Provides
    @Singleton
    fun provideTraiteCaisseApi(client: HttpClient): TraiteCaisseApi = TraiteCaisseApiImpl(client)

}