package com.asmtunis.procaisseinventory.data.unite.remote.di

import com.asmtunis.procaisseinventory.data.unite.remote.api.UniteApi
import com.asmtunis.procaisseinventory.data.unite.remote.api.UniteApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object UniteRemoteModule {

    @Provides
    @Singleton
    fun provideUniteApi(client: HttpClient): UniteApi = UniteApiImpl(client)

}