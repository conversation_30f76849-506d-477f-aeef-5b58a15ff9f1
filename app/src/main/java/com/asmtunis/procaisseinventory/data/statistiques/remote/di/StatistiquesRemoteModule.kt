package com.asmtunis.procaisseinventory.data.statistiques.remote.di

import com.asmtunis.procaisseinventory.data.statistiques.remote.api.StatisticsApi
import com.asmtunis.procaisseinventory.data.statistiques.remote.api.StatisticsApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object StatistiquesRemoteModule {
        @Provides
        @Singleton
        fun provideStatisticsApi(client: HttpClient): StatisticsApi = StatisticsApiImpl(client)



    }