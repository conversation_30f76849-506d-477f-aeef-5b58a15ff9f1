package com.asmtunis.procaisseinventory.data.ticket_resto.local.repository

import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import kotlinx.coroutines.flow.Flow


interface TraiteCaisseLocalRepository {


    fun upsertAll(value: List<TraiteCaisse>)
    fun upsert(value: TraiteCaisse)

    fun updateRegCodeAndState(regCode: String, regCodeM: String)
    fun deleteAll()
    fun deleteByCodeM(codeM: String, exercice: String)
    fun getAll(): Flow<List<TraiteCaisse>>

}