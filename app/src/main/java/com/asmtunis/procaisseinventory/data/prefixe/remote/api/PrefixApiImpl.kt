package com.asmtunis.procaisseinventory.data.prefixe.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.prefixe.domaine.Prefixe
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class PrefixApiImpl(private val client: HttpClient) : PrefixApi {
    override suspend fun getPrefixes(baseConfig: String): Flow<DataResult<List<Prefixe>>> = flow {

        val result = executePostApiCall<List<Prefixe>>(
            client = client,
            endpoint = Urls.GET_PREFIXES,
            baseConfig = baseConfig
        )
        emitAll(result)
    }
}