package com.asmtunis.procaisseinventory.data.statistiques.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.statistiques.local.dao.StatisticsDAO
import com.asmtunis.procaisseinventory.data.statistiques.local.repository.StatisticsLocalRepository
import com.asmtunis.procaisseinventory.data.statistiques.local.repository.StatisticsLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton




@Module
@InstallIn(SingletonComponent::class)
class StatistiquesLocalModule {

    @Provides
    @Singleton
    fun provideStatistiquesDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.statisticsDAO()

    @Provides
    @Singleton
    @Named("Statistiques")
    fun provideStatistiquesRepository(
        statistiquesDAO: StatisticsDAO
    ): StatisticsLocalRepository = StatisticsLocalRepositoryImpl(
        statisticsDAO = statistiquesDAO

    )

}