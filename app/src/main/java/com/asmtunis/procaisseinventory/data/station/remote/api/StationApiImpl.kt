package com.asmtunis.procaisseinventory.data.station.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.station.domaine.PaginationResponseStationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class StationApiImpl(private val client: HttpClient) : StationApi {
    override suspend fun getStations(baseConfig: String): Flow<DataResult<List<Station>>> = flow {
        val result = executePostApiCall<List<Station>>(
            client = client,
            endpoint = Urls.GET_STATIONS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getstockArticle(baseConfig: String): Flow<DataResult<List<StationStockArticle>>> = flow {
        val result = executePostApiCall<List<StationStockArticle>>(
            client = client,
            endpoint = Urls.GET_STOCK_ARTICLE_BY_STATIONS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getstockArticlePagination(
        baseConfig: String,
        page: String,
        limit: String
    ): Flow<DataResult<PaginationResponseStationStockArticle>>  = flow {
        val queryParams = mapOf(
            "page" to page,
            "limit" to limit
        )

        val result = executePostApiCall<PaginationResponseStationStockArticle>(
            client = client,
            endpoint = Urls.GET_STOCK_ARTICLE_BY_STATIONS_PAGINATION,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result)
    }
}