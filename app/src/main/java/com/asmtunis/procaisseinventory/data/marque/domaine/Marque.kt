package com.asmtunis.procaisseinventory.data.marque.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProInventoryConstants.MARQUE_TABLE)
@Serializable
data class Marque(
    @SerialName("MAR_Code")
    @ColumnInfo(name = "MAR_Code")
    @PrimaryKey
    var mARCode: String ="",


    @ColumnInfo(name = "MAR_Designation")
    @SerialName("MAR_Designation")
    var mARDesignation :String = "",

    @ColumnInfo(name = "MAR_Station")
    @SerialName("MAR_Station")
    var mARStation: String? = "",

    @ColumnInfo(name = "MAR_User")
    @SerialName("MAR_User")
    var mARUser: String? = "",

    @ColumnInfo(name = "MAR_export")
    @SerialName("MAR_export")
    var mARExport: String? = "",

    @ColumnInfo(name = "MAR_DDm")
    @SerialName("MAR_DDm")
    var mARDDm: String? = "",

    @ColumnInfo(name = "photo_PathM")
    @SerialName("photo_PathM")
    var photoPathM: String? = "",

) : BaseModel()
