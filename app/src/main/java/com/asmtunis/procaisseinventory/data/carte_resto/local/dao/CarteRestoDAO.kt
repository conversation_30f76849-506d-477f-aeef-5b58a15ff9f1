package com.asmtunis.procaisseinventory.data.carte_resto.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.CARTE_RESTO_TABLE
import com.asmtunis.procaisseinventory.data.carte_resto.domaine.CarteResto
import kotlinx.coroutines.flow.Flow


@Dao
interface CarteRestoDAO {
    @get:Query("SELECT * FROM $CARTE_RESTO_TABLE")
    val all: Flow<List<CarteResto>>

    @Query("SELECT * FROM $CARTE_RESTO_TABLE WHERE Code =:code")
    fun getOneByCode(code: String): Flow<CarteResto>

    @Query("SELECT * FROM $CARTE_RESTO_TABLE WHERE Societe =:societe")
    fun getOneByName(societe: String): Flow<CarteResto>

    @get:Query("SELECT * FROM $CARTE_RESTO_TABLE LIMIT 1")
    val one: Flow<CarteResto>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: CarteResto)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<CarteResto>)

    @Query("DELETE FROM $CARTE_RESTO_TABLE")
    fun deleteAll()
}
