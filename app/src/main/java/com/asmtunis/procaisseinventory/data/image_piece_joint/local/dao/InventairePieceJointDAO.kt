package com.asmtunis.procaisseinventory.data.image_piece_joint.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.PIECE_JOINTS_TABLE
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import kotlinx.coroutines.flow.Flow


@Dao
interface InventairePieceJointDAO {
    @get:Query("SELECT * FROM $PIECE_JOINTS_TABLE order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    val all: Flow<List<ImagePieceJoint>>


    @Query("SELECT * FROM $PIECE_JOINTS_TABLE WHERE (Code_Mob =:codeMob  and Code_IMG=:codeArt) or (Code_Mob =:codeArt  and Code_IMG=:codeMob) order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    fun getImageByBarCodeLive(
        codeMob: String,
        codeArt: String
    ): Flow<List<ImagePieceJoint>>

    @Query("SELECT * FROM $PIECE_JOINTS_TABLE WHERE Code_Mob =:codeMob  and Code_IMG=:codeArt order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    fun getImageByBarCode(codeMob: String, codeArt: String): Flow<ImagePieceJoint>

    @Query("SELECT * FROM $PIECE_JOINTS_TABLE where Code_IMG =:codeIMG or Code_Mob=:codeIMG order by strftime('%Y-%m-%d %H-%M',DateOp) desc")
    fun getByCodeImage(codeIMG: String): Flow<ImagePieceJoint>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(imagePieceJoints: List<ImagePieceJoint>)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(imagePieceJoints: ImagePieceJoint)



    @Query("SELECT * FROM $PIECE_JOINTS_TABLE where isSync=0 and  (Status='INSERTED'  or Status='UPDATED') and  Type_VC Like :typeVC")
    fun noSynced(typeVC: String): Flow<List<ImagePieceJoint>>



    @Query("UPDATE $PIECE_JOINTS_TABLE SET isSync = 1, Status= 'SELECTED'  where Code_IMG = :codeImage and isSync = 0")
    fun updateImageState(codeImage: String)

    @Query("delete from $PIECE_JOINTS_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $PIECE_JOINTS_TABLE where  (Code_Mob =:codeMob  and Code_IMG=:codeArt) or ( Code_IMG =:codeMob  and Code_Mob =:codeArt)")
    fun deleteByCode(codeMob: String, codeArt: String)


    @Query("DELETE FROM $PIECE_JOINTS_TABLE where  devNum =:devNum  and isSync=0")
    fun deleteByDevNumNotSync(devNum: String)


    @Query("UPDATE $PIECE_JOINTS_TABLE SET Status= 'INSERTED' where devNum =:devNum  and isSync=0")
    fun setToInserted(devNum: String)
    @Query("DELETE FROM $PIECE_JOINTS_TABLE where  Code_IMG =:codeImage ")
    fun deleteByCodeImg(codeImage: String)

    @Delete
    fun delete(image: ImagePieceJoint)
}

