package com.asmtunis.procaisseinventory.data.timbre.remote.di

import com.asmtunis.procaisseinventory.data.timbre.remote.api.TimbreApi
import com.asmtunis.procaisseinventory.data.timbre.remote.api.TimbreApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object TimbreRemoteModule {


    @Provides
    @Singleton
    fun provideTimbreApi(client: HttpClient): TimbreApi = TimbreApiImpl(client)
}