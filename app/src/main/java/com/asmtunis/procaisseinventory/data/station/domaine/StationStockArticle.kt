package com.asmtunis.procaisseinventory.data.station.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.STATION_ARTICLE_TABLE
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = STATION_ARTICLE_TABLE, primaryKeys = ["SART_CodeSatation", "SART_CodeArt"], indices = [Index("SART_CodeArt"), Index("SART_CodeSatation")])
@Serializable
data class StationStockArticle(

    @SerialName("SART_CodeSatation")
    @ColumnInfo(name = "SART_CodeSatation")
    var sARTCodeSatation: String = "",
    @SerialName("SART_CodeArt")
    @ColumnInfo(name = "SART_CodeArt")
    var sARTCodeArt: String = "",
    @SerialName("SART_Qte")
    @ColumnInfo(name = "SART_Qte")
    var sARTQteStation: String? = "",
    @SerialName("SART_user")
    @ColumnInfo(name = "SART_user")
    var sARTUser: String? = "",
    @SerialName("SART_DDm")
    @ColumnInfo(name = "SART_DDm")
    var sARTDDm: String? = "",

    @ColumnInfo(name = "SART_QteDeclaree")
    @SerialName("SART_QteDeclaree")
    var sARTQteDeclaree: String? = "",

    @ColumnInfo(name = "SART_QTEmin")
    @SerialName("SART_QTEmin")
    var sARTQTEmin: String? = "",

    @ColumnInfo(name = "SART_QTEmax")
    @SerialName("SART_QTEmax")
    var sARTQTEmax: String? = "",

    @ColumnInfo(name = "SART_station")
    @SerialName("SART_station")
    var sARTStation: String? = "",

    @ColumnInfo(name = "SART_export")
    @SerialName("SART_export")
    var sARTExport: String? = "",

    @ColumnInfo(name = "Regularisation")
    @SerialName("Regularisation")
    var regularisation: String? = ""
)




