package com.asmtunis.procaisseinventory.data.ticket_resto.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.TICKET_RESTO_TABLE
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import kotlinx.coroutines.flow.Flow


@Dao
interface TraiteCaisseDAO {
    @get:Query("SELECT * FROM $TICKET_RESTO_TABLE")
    val all: Flow<List<TraiteCaisse>>

    @Query("SELECT * FROM $TICKET_RESTO_TABLE WHERE TRAIT_Num_M = :code")
    fun getByReglementM(code: String): Flow<List<TraiteCaisse>>

    @Query("SELECT * FROM $TICKET_RESTO_TABLE WHERE TRAIT_Num = :num ")
    fun getOneByCode(num: String): Flow<TraiteCaisse>

    @Query("SELECT * FROM $TICKET_RESTO_TABLE WHERE   TRAIT_Num_M =:rEGCNumTicket")
    fun getByTicket(rEGCNumTicket: String): Flow<List<TraiteCaisse>>

    @get:Query("SELECT * FROM $TICKET_RESTO_TABLE LIMIT 1")
    val one: Flow<TraiteCaisse>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: TraiteCaisse)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<TraiteCaisse>)

    @Query("DELETE FROM $TICKET_RESTO_TABLE")
    fun deleteAll()

    @Query("DELETE FROM ${TICKET_RESTO_TABLE} where TRAIT_Num_M=:codeM and TRAIT_Exercice = :exercice")
    fun deleteByCodeM(codeM: String, exercice: String)


    @Query("UPDATE $TICKET_RESTO_TABLE SET TRAIT_Reglement=:regCode , Status = 'SELECTED' , IsSync = 1 where TRAIT_Reglement = :regCodeM")
    fun updateRegCodeAndState(regCode: String, regCodeM: String)
}

