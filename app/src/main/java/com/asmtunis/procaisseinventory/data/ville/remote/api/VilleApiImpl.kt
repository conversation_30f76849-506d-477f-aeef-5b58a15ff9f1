package com.asmtunis.procaisseinventory.data.ville.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class VilleApiImpl(private val client: HttpClient) : VilleApi {
    override suspend fun getVilles(baseConfig: String): Flow<DataResult<List<Ville>>> = flow {

        val result = executePostApiCall<List<Ville>>(
            client = client,
            endpoint = Urls.GET_VILLE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }


}