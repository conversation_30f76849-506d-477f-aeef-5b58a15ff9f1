package com.asmtunis.procaisseinventory.data.station.remote.di

import com.asmtunis.procaisseinventory.data.station.remote.api.StationApi
import com.asmtunis.procaisseinventory.data.station.remote.api.StationApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object StationModule {

        @Provides
        @Singleton
        fun provideStationApi(client: HttpClient): StationApi = StationApiImpl(client)

    }
