package com.asmtunis.procaisseinventory.data.sessioncaisse.domaine

//import kotlinx.parcelize.Parcelize

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.SESSION_CAISSE_TABLE, primaryKeys = ["SC_IdSCaisse"])
@Serializable
//@Parcelize

data class SessionCaisse  (
    @ColumnInfo(name = "SC_IdSCaisse")
    @SerialName("SC_IdSCaisse")
    val sCIdSCaisse: String = "",

    @ColumnInfo(name = "SC_Caisse")
    @SerialName("SC_Caisse")
    val sCCaisse: String? = "",

    @ColumnInfo(name = "SC_CodeUtilisateur")
    @SerialName("SC_CodeUtilisateur")
    val sCCodeUtilisateur :Int = 0,

    @ColumnInfo(name = "SC_DateHeureOuv")
    @SerialName("SC_DateHeureOuv")
    val sCDateHeureOuv: String? = "",

    @ColumnInfo(name = "SC_ClotCaisse")
    @SerialName("SC_ClotCaisse")
    val sCClotCaisse :Int = 0,

    @ColumnInfo(name = "SC_DateHeureClot")
    @SerialName("SC_DateHeureClot")
    val sCDateHeureClot: String? = "",

    @ColumnInfo(name = "SC_FondCaisse")
    @SerialName("SC_FondCaisse")
    val sCFondCaisse: String? = "",

    @ColumnInfo(name = "SC_TotRemise")
    @SerialName("SC_TotRemise")
    val sCTotRemise: String? = "",

    @ColumnInfo(name = "SC_BenificeTVA")
    @SerialName("SC_BenificeTVA")
    val sCBenificeTVA : Double = 0.0,

    @ColumnInfo(name = "SC_BenificeSansTVA")
    @SerialName("SC_BenificeSansTVA")
    val sCBenificeSansTVA : Double = 0.0,

    @SerialName("SC_TotBenifice")
    @ColumnInfo(name = "SC_TotBenifice")
    val sCTotBenifice : Double = 0.0,

    @ColumnInfo(name = "SC_TotalRecette")
    @SerialName("SC_TotalRecette")
    val sCTotalRecette : Double = 0.0,

    @ColumnInfo(name = "SC_TotalCaisse")
    @SerialName("SC_TotalCaisse")
    val sCTotalCaisse : Double = 0.0,

    @SerialName("SC_IdCarnet")
    @ColumnInfo(name = "SC_IdCarnet")
    val sCIdCarnet: String? = "",

    @SerialName("SC_Etat")
    @ColumnInfo(name = "SC_Etat")
    val sCEtat :Int = 0,

    @ColumnInfo(name = "SC_Facturer")
    @SerialName("SC_Facturer")
    val sCFacturer :Int = 0,

    @ColumnInfo(name = "SC_NumFact")
    @SerialName("SC_NumFact")
    val sCNumFact: String? = "",

    @ColumnInfo(name = "SC_Station")
    @SerialName("SC_Station")
    val sCStation: String? = "",

    @ColumnInfo(name = "SC_User")
    @SerialName("SC_User")
    val sCUser: String? = "",

    @ColumnInfo(name = "SC_DateHeureCrea")
    @SerialName("SC_DateHeureCrea")
    val sCDateHeureCrea: String? = "",

    @ColumnInfo(name = "SC_TotDepense")
    @SerialName("SC_TotDepense")
    val sCTotDepense : Double = 0.0,

    @SerialName("SC_export")
    @ColumnInfo(name = "SC_export")
    val sCExport: String? = "",

    @SerialName("SC_DDm")
    @ColumnInfo(name = "SC_DDm")
    val sCDDm: String? = "",

    @ColumnInfo(name = "SC_TotBenificeRes")
    @SerialName("SC_TotBenificeRes")
    val sCTotBenificeRes: String? = "",

    @ColumnInfo(name = "SC_TotalRecetteManuel")
    @SerialName("SC_TotalRecetteManuel")
    val sCTotalRecetteManuel : Double = 0.0,

    @ColumnInfo(name = "ddm")
    @SerialName("ddm")
    val ddm: String? = "",

    @ColumnInfo(name = "export")
    @SerialName("export")
    val export :Int = 0

    ) : BaseModel()//, Parcelable
