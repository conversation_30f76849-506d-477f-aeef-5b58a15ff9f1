package com.asmtunis.procaisseinventory.data.famille.local.repository

import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import kotlinx.coroutines.flow.Flow



interface FamilleLocalRepository {
    fun upsertAll(value: List<Famille>)
    fun upsert(value: Famille)
    fun deleteAll()

    fun updateSyncFamille(famCode : String)
    fun getAll(): Flow<List<Famille>>
    fun getNotSync(): Flow<List<Famille>>

    fun getNewCode(prefix: String): Flow<String>

}