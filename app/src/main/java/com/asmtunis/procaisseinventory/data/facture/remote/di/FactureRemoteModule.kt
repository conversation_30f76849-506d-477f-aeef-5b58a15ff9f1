package com.asmtunis.procaisseinventory.data.facture.remote.di

import com.asmtunis.procaisseinventory.data.facture.remote.api.FactureApi
import com.asmtunis.procaisseinventory.data.facture.remote.api.FactureApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object FactureRemoteModule {

    @Provides
    @Singleton
    fun provideFactureApi(client: HttpClient): FactureApi = FactureApiImpl(client)

}