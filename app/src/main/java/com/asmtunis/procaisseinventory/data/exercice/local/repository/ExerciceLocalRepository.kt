package com.asmtunis.procaisseinventory.data.exercice.local.repository

import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import kotlinx.coroutines.flow.Flow




interface ExerciceLocalRepository {


    fun upsertAll(value: List<Exercice>)
    fun upsert(value: Exercice)


    fun deleteAll()

    fun getAll(): Flow<List<Exercice>?>
    fun getOneById(code : String): Flow<Exercice?>

}