package com.asmtunis.procaisseinventory.data.station.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.station.domaine.PaginationResponseStationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import kotlinx.coroutines.flow.Flow


interface StationApi {



        suspend fun getStations(baseConfig: String): Flow<DataResult<List<Station>>>
        suspend fun getstockArticle(baseConfig: String): Flow<DataResult<List<StationStockArticle>>>
        suspend fun getstockArticlePagination(baseConfig: String, page: String, limit: String): Flow<DataResult<PaginationResponseStationStockArticle>>
}

