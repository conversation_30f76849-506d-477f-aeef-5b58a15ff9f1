package com.asmtunis.procaisseinventory.data.banques.remote.di

import com.asmtunis.procaisseinventory.data.banques.remote.api.BanqueApi
import com.asmtunis.procaisseinventory.data.banques.remote.api.BanqueApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object BanqueRemoteModule {

    @Provides
    @Singleton
    fun provideBanqueApi(client: HttpClient): BanqueApi = BanqueApiImpl(client)

}