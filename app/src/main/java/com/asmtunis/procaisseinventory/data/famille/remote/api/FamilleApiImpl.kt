package com.asmtunis.procaisseinventory.data.famille.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.famille.domaine.AddFamilleResponse
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class FamilleApiImpl(private val client: HttpClient) : FamilleApi {
    override suspend fun getFamilles(baseConfig: String): Flow<DataResult<List<Famille>>> = flow {
        val result = executePostApiCall<List<Famille>>(
            client = client,
            endpoint = Urls.GET_FAMILLES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addFamilleMobile(baseConfig: String): Flow<DataResult<List<AddFamilleResponse>>> = flow {

        val result = executePostApiCall<List<AddFamilleResponse>>(
            client = client,
            endpoint = Urls.ADD_FAMILLE_MOBILE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }