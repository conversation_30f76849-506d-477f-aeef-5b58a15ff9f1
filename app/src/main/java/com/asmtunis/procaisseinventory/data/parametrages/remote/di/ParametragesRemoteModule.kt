package com.asmtunis.procaisseinventory.data.parametrages.remote.di

import com.asmtunis.procaisseinventory.data.parametrages.remote.api.ParametragesApi
import com.asmtunis.procaisseinventory.data.parametrages.remote.api.ParametragesApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object ParametragesRemoteModule {
        @Provides
        @Singleton
        fun provideParametragesApi(client: HttpClient): ParametragesApi = ParametragesApiImpl(client)

    }