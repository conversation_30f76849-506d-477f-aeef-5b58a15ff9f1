package com.asmtunis.procaisseinventory.data.carte_resto.remote.di

import com.asmtunis.procaisseinventory.data.carte_resto.remote.api.CarteRestoApi
import com.asmtunis.procaisseinventory.data.carte_resto.remote.api.CarteRestoApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object CarteRestoRemoteModule {

    @Provides
    @Singleton
    fun provideCarteRestoApi(client: HttpClient): CarteRestoApi = CarteRestoApiImpl(client)


}