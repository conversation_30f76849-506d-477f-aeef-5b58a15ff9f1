package com.asmtunis.procaisseinventory.data.facture.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Entity(tableName = ProCaisseConstants.FACTURE_TABLE, primaryKeys = ["FACT_Num", "FACT_Exerc"])
@Serializable
data class Facture(
    @ColumnInfo(name = "FACT_Num")
    @SerialName("FACT_Num")
    val factNum: String = "",
    @ColumnInfo(name = "FACT_Exerc")
    @SerialName("FACT_Exerc")
    val factExerc: String = "",
    @ColumnInfo(name = "FACT_AdresseCli")
    @SerialName("FACT_AdresseCli")
    val factAdresseCli: String? = null,
    @ColumnInfo(name = "FACT_CodeClient")
    @SerialName("FACT_CodeClient")
    val factCodeClient: String? = null,
    @ColumnInfo(name = "FACT_Comptabiliser")
    @SerialName("FACT_Comptabiliser")
    val factComptabiliser: String? = null,
    @ColumnInfo(name = "FACT_DDm")
    @SerialName("FACT_DDm")
    val factDDm: String? = null,
    @ColumnInfo(name = "FACT_Date")
    @SerialName("FACT_Date")
    val factDate: String? = null,
    @ColumnInfo(name = "FACT_Etat")
    @SerialName("FACT_Etat")
    val factEtat: String? = null,
    @ColumnInfo(name = "FACT_ExoNum")
    @SerialName("FACT_ExoNum")
    val factExoNum: String? = null,
    @ColumnInfo(name = "FACT_ExoVal")
    @SerialName("FACT_ExoVal")
    val factExoVal: String? = null,
    @ColumnInfo(name = "FACT_Exonoration")
    @SerialName("FACT_Exonoration")
    val factExonoration: String? = null,
    @ColumnInfo(name = "FACT_FactFournisseur")
    @SerialName("FACT_FactFournisseur")
    val factFactFournisseur: String? = null,
    @ColumnInfo(name = "FACT_MatFisc")
    @SerialName("FACT_MatFisc")
    val factMatFisc: String? = null,
    @ColumnInfo(name = "FACT_MntHT")
    @SerialName("FACT_MntHT")
    val factMntHT: String? = null,
    @ColumnInfo(name = "FACT_MntNetHT")
    @SerialName("FACT_MntNetHT")
    val factMntNetHT: String? = null,
    @ColumnInfo(name = "FACT_MntRemise")
    @SerialName("FACT_MntRemise")
    val factMntRemise: String? = null,
    @ColumnInfo(name = "FACT_MntRevImp")
    @SerialName("FACT_MntRevImp")
    val factMntRevImp: String? = null,
    @ColumnInfo(name = "FACT_MntTTC")
    @SerialName("FACT_MntTTC")
    val factMntTTC: String? = null,
    @ColumnInfo(name = "FACT_MntTva")
    @SerialName("FACT_MntTva")
    val factMntTva: String? = null,
    @ColumnInfo(name = "FACT_NomPrenomCli")
    @SerialName("FACT_NomPrenomCli")
    val factNomPrenomCli: String? = null,
    @ColumnInfo(name = "FACT_NumBC")
    @SerialName("FACT_NumBC")
    val factNumBC: String? = null,
    @ColumnInfo(name = "FACT_Reg")
    @SerialName("FACT_Reg")
    val factReg: String? = null,
    @ColumnInfo(name = "FACT_Regler")
    @SerialName("FACT_Regler")
    val factRegler: String? = null,
    @ColumnInfo(name = "FACT_RetSource")
    @SerialName("FACT_RetSource")
    val factRetSource: String? = null,
    @ColumnInfo(name = "FACT_TauxRemise")
    @SerialName("FACT_TauxRemise")
    val factTauxRemise: String? = null,
    @ColumnInfo(name = "FACT_Timbre")
    @SerialName("FACT_Timbre")
    val factTimbre: String? = null,
    @ColumnInfo(name = "FACT_Total_HDC")
    @SerialName("FACT_Total_HDC")
    val factTotal_HDC: String? = null,
    @ColumnInfo(name = "FACT_Total_HTVA")
    @SerialName("FACT_Total_HTVA")
    val factTotal_HTVA: String? = null,
    @ColumnInfo(name = "FACT_Type")
    @SerialName("FACT_Type")
    val factType: String? = null,
    @ColumnInfo(name = "FACT_TypeReg")
    @SerialName("FACT_TypeReg")
    val factTypeReg: String? = null,
    @ColumnInfo(name = "FACT_ValiditeTraite")
    @SerialName("FACT_ValiditeTraite")
    val factValiditeTraite: String? = null,
    @ColumnInfo(name = "FACT_export")
    @SerialName("FACT_export")
    val factexport: String? = null,
    @ColumnInfo(name = "FACT_station")
    @SerialName("FACT_station")
    val factstation: String? = null,
    @ColumnInfo(name = "FACT_user")
    @SerialName("FACT_user")
    val factuser: String? = null
)