package com.asmtunis.procaisseinventory.data.exercice.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.data.exercice.domaine.Exercice
import kotlinx.coroutines.flow.Flow



@Dao
interface ExerciceDAO {
    @get:Query("SELECT * FROM ${ProCaisseConstants.EXERCICE_TABLE}")
    val all: Flow<List<Exercice>?>

    @Query("SELECT * FROM ${ProCaisseConstants.EXERCICE_TABLE} WHERE lower(Exercice_Code) = lower(:code) ")
    fun getOneById(code: String?): Flow<Exercice>

    @Query("SELECT COUNT(*) FROM ${ProCaisseConstants.EXERCICE_TABLE}")
    fun count(): Int


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Exercice)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Exercice>)

    @Query("DELETE FROM ${ProCaisseConstants.EXERCICE_TABLE}")
    fun deleteAll()
}