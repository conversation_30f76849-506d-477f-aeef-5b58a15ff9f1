package com.asmtunis.procaisseinventory.data.facture.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.FACTURE_TABLE
import com.asmtunis.procaisseinventory.data.facture.domaine.Facture
import kotlinx.coroutines.flow.Flow

@Dao
interface FactureDAO {
    @get:Query("SELECT * FROM $FACTURE_TABLE")
    val all: Flow<List<Facture?>?>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Facture)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Facture>)

    @Query("DELETE FROM $FACTURE_TABLE")
    fun deleteAll()

    @Query("SELECT * FROM $FACTURE_TABLE WHERE FACT_NumBC = :code ")
    fun getByFactNumBc(code: Int): Flow<Facture?>

    @Query("SELECT * FROM Facture WHERE FACT_NumBC = :code and FACT_user = :user and FACT_station = :station ")
    fun getByTicket(code: String?, user: String, station: String): Flow<Facture?>
}
