package com.asmtunis.procaisseinventory.data.ticket_resto.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.ticket_resto.domaine.TraiteCaisse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class TraiteCaisseApiImpl(private val client: HttpClient) : TraiteCaisseApi {

    override suspend fun getTraiteCaisseByReglements(baseConfig: String): Flow<DataResult<List<List<TraiteCaisse>>>> = flow {
        val result = executePostApiCall<List<List<TraiteCaisse>>>(
            client = client,
            endpoint = Urls.GET_TRAITE_CAISSE_BY_REGLEMENTS,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }