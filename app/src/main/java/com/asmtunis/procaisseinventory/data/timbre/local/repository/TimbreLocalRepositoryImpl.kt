package com.asmtunis.procaisseinventory.data.timbre.local.repository

import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import com.asmtunis.procaisseinventory.data.timbre.local.dao.TimbreDAO
import kotlinx.coroutines.flow.Flow


class TimbreLocalRepositoryImpl(private val timbreDAO: TimbreDAO) : TimbreLocalRepository {
    override fun upsertAll(value: List<Timbre>)  = timbreDAO.insertAll(value)

    override fun deleteAll() = timbreDAO.deleteAll()

    override fun getAll(): Flow<List<Timbre>> = timbreDAO.all

    override fun getActif(): Flow<List<Timbre>> = timbreDAO.actif

}