package com.asmtunis.procaisseinventory.data.parametrages.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.parametrages.domaine.Parametrages
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class ParametragesApiImpl(private val client: HttpClient) : ParametragesApi {
    override suspend fun getParametrage(baseConfig: String): Flow<DataResult<Parametrages>> = flow {
        val result = executePostApiCall<Parametrages>(
            client = client,
            endpoint = Urls.GET_PARAMETRAGE,
            baseConfig = baseConfig
        )
        emitAll(result)
    }
}