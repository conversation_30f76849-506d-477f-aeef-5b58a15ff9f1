package com.asmtunis.procaisseinventory.data.ville.remote.di

import com.asmtunis.procaisseinventory.data.ville.remote.api.VilleApi
import com.asmtunis.procaisseinventory.data.ville.remote.api.VilleApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object VilleeRemoteModule {


    @Provides
    @Singleton
    fun provideVilleApi(client: HttpClient): VilleApi = VilleApiImpl(client)

}