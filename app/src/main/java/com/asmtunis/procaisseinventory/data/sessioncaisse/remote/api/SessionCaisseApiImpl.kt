package com.asmtunis.procaisseinventory.data.sessioncaisse.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.AddSessionCaisseResponse
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class SessionCaisseApiImpl(private val client: HttpClient) : SessionCaisseApi {
    override suspend fun getSessionCaisses(baseConfig: String): Flow<DataResult<List<SessionCaisse>>> = flow {

        val result = executePostApiCall<List<SessionCaisse>>(
            client = client,
            endpoint = Urls.GET_SESSION_CAISSES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addSessionVendeur(
        baseConfig: String,
        sCIdSCaisse: String
    ): Flow<DataResult<AddSessionCaisseResponse>> = flow {

        val result = executePostApiCall<AddSessionCaisseResponse>(
            client = client,
            endpoint = Urls.ADD_SESSION_VENDEUR,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addSession(
        baseConfig: String,
        sCIdSCaisse: String
    ): Flow<DataResult<AddSessionCaisseResponse>> = flow {

        val parameters = mapOf("SC_IdSCaisse" to sCIdSCaisse)
        val result = executePostApiCall<AddSessionCaisseResponse>(
            client = client,
            queryParams = parameters,
            endpoint = Urls.ADD_SESSION,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun closeSessionVendeur(
        baseConfig: String,
        facture: Boolean
    ): Flow<DataResult<Boolean>> = flow {

        val parameters = mapOf("facture" to facture.toString())

        val result = executePostApiCall<Boolean>(
            client = client,
            endpoint = Urls.CLOSE_SESSION_VENDEUR,
            queryParams = parameters,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun getSessionCaisseByUser(baseConfig: String): Flow<DataResult<SessionCaisse>> = flow {

        val result = executePostApiCall<SessionCaisse>(
            client = client,
            endpoint = Urls.GET_SESSION_CAISSE_BY_USER,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

}