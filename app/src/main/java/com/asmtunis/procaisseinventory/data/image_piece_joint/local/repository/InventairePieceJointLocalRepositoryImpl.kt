package com.asmtunis.procaisseinventory.data.image_piece_joint.local.repository

import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.data.image_piece_joint.local.dao.InventairePieceJointDAO
import kotlinx.coroutines.flow.Flow


class InventairePieceJointLocalRepositoryImpl(
        private val inventairePieceJointDAO: InventairePieceJointDAO
    ) : InventairePieceJointLocalRepository {
    override fun updateImageState(codeImage: String) = inventairePieceJointDAO.updateImageState(codeImage= codeImage)
    override fun noSynced(typeVC: String): Flow<List<ImagePieceJoint>> = inventairePieceJointDAO.noSynced(typeVC = typeVC)

    override fun insertAll(imagePieceJoints: List<ImagePieceJoint>)  = inventairePieceJointDAO.insertAll(imagePieceJoints = imagePieceJoints)

    override fun insert(imagePieceJoints: ImagePieceJoint)  = inventairePieceJointDAO.insert(imagePieceJoints)

    override fun deleteAll()  = inventairePieceJointDAO.deleteAll()

    override fun deleteByCode(codeMob: String, codeArt: String)  = inventairePieceJointDAO.deleteByCode(codeMob = codeMob, codeArt= codeArt)

    override fun delete(image: ImagePieceJoint)  = inventairePieceJointDAO.delete(image = image)
    override fun deleteByCodeImg(codeImage: String)  = inventairePieceJointDAO.deleteByCodeImg(codeImage= codeImage)
    override fun deleteByDevNumNotSync(devNum: String) = inventairePieceJointDAO.deleteByDevNumNotSync(devNum = devNum)
    override fun setToInserted(devNum: String) = inventairePieceJointDAO.setToInserted(devNum = devNum)
}