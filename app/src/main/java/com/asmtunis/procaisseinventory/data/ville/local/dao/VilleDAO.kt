package com.asmtunis.procaisseinventory.data.ville.local.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.data.ville.domaine.Ville
import kotlinx.coroutines.flow.Flow


@Dao
interface VilleDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOne(ville: Ville)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(villes: List<Ville>)

    @get:Query("SELECT * FROM Ville")
    val villes: Flow<List<Ville>?>

    @Query("Delete from Ville")
    fun deleteAll()

    @Query("select count(*) from Ville")
    fun count(): Flow<Int?>

    @Delete
    fun deleteOne(traking: Ville)
}