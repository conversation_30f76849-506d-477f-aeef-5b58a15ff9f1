package com.asmtunis.procaisseinventory.data.marque.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.marque.domaine.AddMarqueResponse
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class MarqueApiImpl(private val client: HttpClient) : MarqueApi {
    override suspend fun getMarques(baseConfig: String): Flow<DataResult<List<Marque>>> = flow {
        val result = executePostApiCall<List<Marque>>(
            client = client,
            endpoint = Urls.GET_MARQUES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addMarqueMobile(baseConfig: String): Flow<DataResult<List<AddMarqueResponse>>> = flow {
        val result = executePostApiCall<List<AddMarqueResponse>>(
            client = client,
            endpoint = Urls.ADD_MARQUE_MOBILE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }