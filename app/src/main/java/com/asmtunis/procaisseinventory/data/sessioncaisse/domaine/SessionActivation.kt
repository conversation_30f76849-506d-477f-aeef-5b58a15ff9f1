package com.asmtunis.procaisseinventory.data.sessioncaisse.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class SessionActivation (
    @SerialName("session")
    var session: String? = "",

    @SerialName("caisse")
    var caisse: String? = "",

    @SerialName("utilisateur")
    var utilisateur: String? = "",

    @SerialName("carnet")
    var carnet: String? = "",

    @SerialName("station")
    var station: String? = "",

    @SerialName("fondCaisse")
    var fondCaisse: String? = "",

    @SerialName("nomMachine")
    var nomMachine: String? = "",

)

