package com.asmtunis.procaisseinventory.data.station.local.station_stock_article.repository

import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticleWithStation
import kotlinx.coroutines.flow.Flow


interface StationStockArticleLocalRepository {

    fun upsertAll(value: List<StationStockArticle>)
    fun upsert(value: StationStockArticle)
    fun deleteAll()

    fun updateQtePerStation(newQteStation: String, sartQteDeclare: String, codeArticle: String, codeStation: String)
    fun qtePerStation(codeStation: String, codeArticle: String): Flow<String>
    fun getStationStockArticle(codeStation: String, codeArticle: String): Flow<StationStockArticle?>
    fun getAllStationStockArticle(): Flow<List<StationStockArticle>?>

    fun getStationListByProduct(codeArticle: String): Flow<List<StationStockArticleWithStation>?>
}