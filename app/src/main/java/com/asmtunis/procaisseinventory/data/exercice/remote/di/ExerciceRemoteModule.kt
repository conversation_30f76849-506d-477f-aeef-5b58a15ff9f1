package com.asmtunis.procaisseinventory.data.exercice.remote.di

import com.asmtunis.procaisseinventory.data.exercice.remote.api.ExerciceApi
import com.asmtunis.procaisseinventory.data.exercice.remote.api.ExerciceApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object ExerciceRemoteModule {


        @Provides
        @Singleton
        fun provideExerciceApi(client: HttpClient): ExerciceApi = ExerciceApiImpl(client)


    }