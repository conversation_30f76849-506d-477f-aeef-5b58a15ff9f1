package com.asmtunis.procaisseinventory.data.statistiques.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.STATISTIQUES_TABLE/*, primaryKeys = ["id"]*/)
@Serializable
data class Statistics(
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    var id: Int? = null,

    @SerialName("REGC_IdSCaisse")
    @ColumnInfo(name = "REGC_IdSCaisse")
    var rEGCIdSCaisse: String? = null,

    @SerialName("Mnt_Espece")
    @ColumnInfo(name = "Mnt_Espece")
    var mntEspece: String? = null,

    @SerialName("Mnt_Carte_Bancaire")
    @ColumnInfo(name = "Mnt_Carte_Bancaire")
    var mntCarteBancaire: String? = null,

    @SerialName("Mnt_Cheque")
    @ColumnInfo(name = "Mnt_Cheque")
    var mntCheque: String? = null,

    @SerialName("Mnt_Traite")
    @ColumnInfo(name = "Mnt_Traite")
    var mntTraite: String? = null,

    @SerialName("total")
    @ColumnInfo(name = "total")
    var total: String? = null,

    @SerialName("Mnt_Bonus")
    @ColumnInfo(name = "Mnt_Bonus")
    var mntBonus: String? = null,

    @SerialName("Fond_Caisse")
    @ColumnInfo(name = "Fond_Caisse")
    var fondCaisse: String? = null,

    @SerialName("Dep_Caisse")
    @ColumnInfo(name = "Dep_Caisse")
    var depCaisse: String? = null,

    @SerialName("MntCarte_prepayee")
    @ColumnInfo(name = "MntCarte_prepayee")
    var mntCartePrepayee: String? = null,

    @SerialName("Mnt_PointMerci")
    @ColumnInfo(name = "Mnt_PointMerci")
    var mntPointMerci: String? = null,

    @SerialName("MntBonAchat")
    @ColumnInfo(name = "MntBonAchat")
    var mntBonAchat: String? = null,

    @SerialName("NomPrenUtil")
    @ColumnInfo(name = "NomPrenUtil")
    var nomPrenUtil: String? = null,

    @SerialName("CA")
    @ColumnInfo(name = "CA")
    var cA: String? = null,

    @SerialName("Nbr_Client")
    @ColumnInfo(name = "Nbr_Client")
    var nbrClient: String? = null,

    @SerialName("NbreTicket")
    @ColumnInfo(name = "NbreTicket")
    var nbreTicket: String? = null,

    @SerialName("LT_Qte")
    @ColumnInfo(name = "LT_Qte")
    var lTQte: String? = null,

    @SerialName("TopClients")
    @ColumnInfo(name = "TopClients")
    var topClients: List<Client>? = null,

    @SerialName("Mnt_Credit")
    @ColumnInfo(name = "Mnt_Credit")
    var mntCredit: String? = null,

    @SerialName("updated_at")
    @ColumnInfo(name = "updated_at")
    var updated_at: Long? = null

)
