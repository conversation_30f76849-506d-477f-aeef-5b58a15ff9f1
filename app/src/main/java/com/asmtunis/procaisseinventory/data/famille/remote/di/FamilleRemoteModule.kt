package com.asmtunis.procaisseinventory.data.famille.remote.di

import com.asmtunis.procaisseinventory.data.famille.remote.api.FamilleApi
import com.asmtunis.procaisseinventory.data.famille.remote.api.FamilleApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object FamilleRemoteModule {

    @Provides
    @Singleton
    fun provideFamilleApi(client: HttpClient): FamilleApi = FamilleApiImpl(client)

}