package com.asmtunis.procaisseinventory.data.station.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.station.local.station.dao.StationDAO
import com.asmtunis.procaisseinventory.data.station.local.station.repository.StationLocalRepository
import com.asmtunis.procaisseinventory.data.station.local.station.repository.StationLocalRepositoryImpl
import com.asmtunis.procaisseinventory.data.station.local.station_stock_article.dao.StationStockArticleDAO
import com.asmtunis.procaisseinventory.data.station.local.station_stock_article.repository.StationStockArticleLocalRepository
import com.asmtunis.procaisseinventory.data.station.local.station_stock_article.repository.StationStockArticleLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class StationLocalModule {

    @Provides
    @Singleton
    fun provideStationDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.stationDAO()

    @Provides
    @Singleton
    @Named("Station")
    fun provideStationRepository(
        stationDAO: StationDAO
    ): StationLocalRepository = StationLocalRepositoryImpl(
        stationDAO = stationDAO
    )



    @Provides
    @Singleton
    fun provideStationArticleDao(
        proInventoryDataBase: ProCaisseDataBase
    ) = proInventoryDataBase.stationArticleDAO()

    @Provides
    @Singleton
    @Named("StationArticle")
    fun provideStationArticleRepository(
        stationStockArticleDAO: StationStockArticleDAO
    ): StationStockArticleLocalRepository = StationStockArticleLocalRepositoryImpl(
        stationStockArticleDAO = stationStockArticleDAO
    )
}