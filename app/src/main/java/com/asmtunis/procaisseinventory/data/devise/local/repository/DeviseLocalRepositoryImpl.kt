package com.asmtunis.procaisseinventory.data.devise.local.repository

import com.asmtunis.procaisseinventory.data.devise.domaine.Devise
import com.asmtunis.procaisseinventory.data.devise.local.dao.DeviseDAO
import kotlinx.coroutines.flow.Flow


class DeviseLocalRepositoryImpl(private val deviseDAO: DeviseDAO) : DeviseLocalRepository {
    override fun upsertAll(value: List<Devise>) = deviseDAO.insertAll(value)

    override fun deleteAll()  = deviseDAO.deleteAll()

    override fun getAll(): Flow<List<Devise>>  = deviseDAO.all

    override fun activeOne(): Flow<Devise?>  = deviseDAO.activeOne

}