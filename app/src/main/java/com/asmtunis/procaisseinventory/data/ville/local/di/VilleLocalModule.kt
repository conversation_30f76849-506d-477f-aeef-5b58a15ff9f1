package com.asmtunis.procaisseinventory.data.ville.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.ville.local.dao.VilleDAO
import com.asmtunis.procaisseinventory.data.ville.local.repository.VilleLocalRepository
import com.asmtunis.procaisseinventory.data.ville.local.repository.VilleLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class VilleLocalModule {

    @Provides
    @Singleton
    fun provideVilleDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.villeDAO()

    @Provides
    @Singleton
    @Named("Ville")
    fun provideVilleRepository(
        villeDAO: VilleDAO
    ): VilleLocalRepository = VilleLocalRepositoryImpl(
        villeDAO = villeDAO
    )

}