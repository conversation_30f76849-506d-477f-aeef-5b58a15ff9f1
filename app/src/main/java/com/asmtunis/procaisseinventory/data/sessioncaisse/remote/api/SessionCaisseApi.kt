package com.asmtunis.procaisseinventory.data.sessioncaisse.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.AddSessionCaisseResponse
import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import kotlinx.coroutines.flow.Flow

interface SessionCaisseApi {



    suspend fun getSessionCaisses(baseConfig: String): Flow<DataResult<List<SessionCaisse>>>

    //ONLY CREATE A NEW SESSION
    suspend fun addSessionVendeur(baseConfig: String, sCIdSCaisse : String): Flow<DataResult<AddSessionCaisseResponse>>

    //CREATE + CLOSE CURRENT SESSION
    suspend fun addSession(baseConfig: String, sCIdSCaisse : String): Flow<DataResult<AddSessionCaisseResponse>>
    suspend fun closeSessionVendeur(baseConfig: String, facture: Boolean): Flow<DataResult<Boolean>>
    suspend fun getSessionCaisseByUser(baseConfig: String): Flow<DataResult<SessionCaisse>>
}
