package com.asmtunis.procaisseinventory.data.cheque_caisse.local.repository

import com.asmtunis.procaisseinventory.data.cheque_caisse.domaine.ChequeCaisse
import kotlinx.coroutines.flow.Flow



interface ChequeCaisseLocalRepository {

    fun upsertAll(value: List<ChequeCaisse>)

    fun updateRegCodeAndState(regCode: String, regCodeM: String)
    fun deleteAll()
    fun deleteByCodeM(codeM: String, exercice: String)
    fun getAll(): Flow<List<ChequeCaisse>>

    fun getByReglementM(code: String): Flow<List<ChequeCaisse>>

}