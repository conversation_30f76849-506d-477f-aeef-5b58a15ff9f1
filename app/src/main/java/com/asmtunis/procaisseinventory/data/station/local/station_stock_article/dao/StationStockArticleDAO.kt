package com.asmtunis.procaisseinventory.data.station.local.station_stock_article.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProInventoryConstants.Companion.STATION_ARTICLE_TABLE
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticle
import com.asmtunis.procaisseinventory.data.station.domaine.StationStockArticleWithStation
import kotlinx.coroutines.flow.Flow


@Dao
interface StationStockArticleDAO {




    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(stationStockArticleList: List<StationStockArticle>)

    @Query("DELETE FROM $STATION_ARTICLE_TABLE")
    fun deleteAll()

    @Query("select * FROM  $STATION_ARTICLE_TABLE  where (SART_CodeArt =:codeArticle)")
    @Transaction
    fun getStationListByProduct(codeArticle: String): Flow<List<StationStockArticleWithStation>?>

    @Query("SELECT SART_Qte FROM $STATION_ARTICLE_TABLE WHERE (SART_CodeSatation =:codeStation) AND (SART_CodeArt =:codeArticle)")
    fun qtePerStation(codeStation: String, codeArticle: String): Flow<String>

    @Query("SELECT * FROM $STATION_ARTICLE_TABLE WHERE (SART_CodeSatation =:codeStation) AND (SART_CodeArt =:codeArticle)")
    fun getStationStockArticle(codeStation: String, codeArticle: String): Flow<StationStockArticle?>


    @Query("SELECT * FROM $STATION_ARTICLE_TABLE")
    fun getAllStationStockArticle(): Flow<List<StationStockArticle>?>

    @Query("UPDATE $STATION_ARTICLE_TABLE SET SART_Qte =:newQteStation, SART_QteDeclaree =:sartQteDeclare WHERE (SART_CodeArt=:codeArticle) AND (SART_CodeSatation=:codeStation)")
    fun updateQtePerStation(newQteStation: String, sartQteDeclare: String, codeArticle: String, codeStation: String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: StationStockArticle)
}
