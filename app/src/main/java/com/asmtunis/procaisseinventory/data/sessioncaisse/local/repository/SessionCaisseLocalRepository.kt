package com.asmtunis.procaisseinventory.data.sessioncaisse.local.repository

import com.asmtunis.procaisseinventory.data.sessioncaisse.domaine.SessionCaisse
import kotlinx.coroutines.flow.Flow

interface SessionCaisseLocalRepository {

    fun upsert(value: SessionCaisse) // : Long

    fun upsertAll(value: List<SessionCaisse>)

    fun deleteAll()

    fun getAll(): Flow<List<SessionCaisse>>
    fun getNotSync(): Flow<List<SessionCaisse>>

    fun getByCode(code: String): Flow<SessionCaisse>
}
