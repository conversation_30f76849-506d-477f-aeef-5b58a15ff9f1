package com.asmtunis.procaisseinventory.data.sessioncaisse.remote.di

import com.asmtunis.procaisseinventory.data.sessioncaisse.remote.api.SessionCaisseApi
import com.asmtunis.procaisseinventory.data.sessioncaisse.remote.api.SessionCaisseApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    object SessionCaisseRemoteModule {
    @Provides
    @Singleton
    fun provideSessionCaisseApi(client: HttpClient): SessionCaisseApi = SessionCaisseApiImpl(client)

}