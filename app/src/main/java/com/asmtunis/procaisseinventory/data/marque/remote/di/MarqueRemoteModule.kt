package com.asmtunis.procaisseinventory.data.marque.remote.di

import com.asmtunis.procaisseinventory.data.marque.remote.api.MarqueApi
import com.asmtunis.procaisseinventory.data.marque.remote.api.MarqueApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object MarqueRemoteModule {

    @Provides
    @Singleton
    fun provideMarqueApi(client: HttpClient): MarqueApi = MarqueApiImpl(client)

}