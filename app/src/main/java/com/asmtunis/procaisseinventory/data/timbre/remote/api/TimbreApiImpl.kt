package com.asmtunis.procaisseinventory.data.timbre.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.timbre.domaine.Timbre
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class TimbreApiImpl(private val client: HttpClient) : TimbreApi {
    override suspend fun getTimbres(baseConfig: String): Flow<DataResult<List<Timbre>>> = flow {
        val result = executePostApiCall<List<Timbre>>(
            client = client,
            endpoint = Urls.GET_ALL_TIMBRE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
        }

