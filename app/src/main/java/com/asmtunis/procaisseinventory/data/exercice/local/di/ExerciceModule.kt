package com.asmtunis.procaisseinventory.data.exercice.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.exercice.local.dao.ExerciceDAO
import com.asmtunis.procaisseinventory.data.exercice.local.repository.ExerciceLocalRepository
import com.asmtunis.procaisseinventory.data.exercice.local.repository.ExerciceLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class ExerciceModule {

    @Provides
    @Singleton
    fun provideExerciceDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.exerciceDAO()

    @Provides
    @Singleton
    @Named("Exercice")
    fun provideExerciceRepository(
        exerciceDAO: ExerciceDAO
    ): ExerciceLocalRepository = ExerciceLocalRepositoryImpl(
       exerciceDAO = exerciceDAO

    )


}