package com.asmtunis.procaisseinventory.data.marque.local.repository

import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import kotlinx.coroutines.flow.Flow


interface MarqueLocalRepository {
    fun upsertAll(value: List<Marque>)
    fun upsert(value: Marque)
    fun deleteAll()
    fun getNewCode(prefix: String): Flow<String>
    fun getNotSync(): Flow<List<Marque>>

    fun updateSyncMarque(marCode : String)
    fun getAll(): Flow<List<Marque>?>

}