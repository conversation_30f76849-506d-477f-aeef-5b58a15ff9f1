package com.asmtunis.procaisseinventory.data.banques.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.BANQUE_TABLE
import com.asmtunis.procaisseinventory.data.banques.domaine.Banque
import kotlinx.coroutines.flow.Flow


@Dao
interface BanqueDAO {
    @get:Query("SELECT * FROM $BANQUE_TABLE")
    val all: Flow<List<Banque>>

    @Query("SELECT * FROM $BANQUE_TABLE WHERE BAN_Code = :code ")
    fun getOneByCode(code: String): Flow<Banque>

    @get:Query("SELECT * FROM $BANQUE_TABLE LIMIT 1")
    val one: Flow<Banque>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Banque)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Banque>)

    @Query("DELETE FROM $BANQUE_TABLE")
    fun deleteAll()
}
