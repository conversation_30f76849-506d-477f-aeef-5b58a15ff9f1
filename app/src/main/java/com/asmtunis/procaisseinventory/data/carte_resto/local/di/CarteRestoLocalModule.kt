package com.asmtunis.procaisseinventory.data.carte_resto.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.data.carte_resto.local.dao.CarteRestoDAO
import com.asmtunis.procaisseinventory.data.carte_resto.local.repository.CarteRestoLocalRepository
import com.asmtunis.procaisseinventory.data.carte_resto.local.repository.CarteRestoLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class CarteRestoLocalModule {

    @Provides
    @Singleton
    fun provideCarteRestoDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.carteRestoDAO()

    @Provides
    @Singleton
    @Named("CarteResto")
    fun provideCarteRestoRepository(
        carteRestoDAO: CarteRestoDAO
    ): CarteRestoLocalRepository = CarteRestoLocalRepositoryImpl(
        carteRestoDAO = carteRestoDAO

    )


}