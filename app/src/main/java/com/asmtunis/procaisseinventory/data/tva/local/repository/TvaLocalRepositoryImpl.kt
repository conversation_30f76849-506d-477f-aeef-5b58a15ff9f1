package com.asmtunis.procaisseinventory.data.tva.local.repository

import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.tva.local.dao.TvaDAO
import kotlinx.coroutines.flow.Flow


class TvaLocalRepositoryImpl(
    private val tvaDAO: TvaDAO
) : TvaLocalRepository {
    override fun upsertAll(value: List<Tva>) =
        tvaDAO.insertAll(value)

    override fun deleteAll() =
        tvaDAO.deleteAll()

    override fun getAll(): Flow<List<Tva>?> =
        tvaDAO.all

}