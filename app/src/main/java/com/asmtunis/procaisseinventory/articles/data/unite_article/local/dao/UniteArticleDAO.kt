package com.asmtunis.procaisseinventory.articles.data.unite_article.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.UNITE_Articles_TABLE
import kotlinx.coroutines.flow.Flow

@Dao
interface UniteArticleDAO {

    @Query("SELECT * FROM $UNITE_Articles_TABLE WHERE UNITE_ARTICLE_CodeArt = :code")
    fun getByCode(code: String?): Flow<UniteArticle>

    @get:Query("SELECT * FROM $UNITE_Articles_TABLE")
    val all: Flow<List<UniteArticle>>


    @get:Query("SELECT * FROM $UNITE_Articles_TABLE LIMIT 1")
    val one: UniteArticle?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: UniteArticle)


    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<UniteArticle>)

    //   @Query("UPDATE UniteArticle SET isSync=1 , Status='SELECTED'")
    //  void updateStatus();
    @Query("DELETE FROM $UNITE_Articles_TABLE")
    fun deleteAll()
}



