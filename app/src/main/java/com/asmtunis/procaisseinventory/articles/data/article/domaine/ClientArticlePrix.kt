package com.asmtunis.procaisseinventory.articles.data.article.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.CLIENTS_ARTICLE_PRIX_TABLE
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = CLIENTS_ARTICLE_PRIX_TABLE, primaryKeys = ["ART_CLI_CodeArt", "ART_CLI_CodeCli"])
@Serializable
data class ClientArticlePrix(
    @SerialName("ART_CLI_CodeArt")
    @ColumnInfo(name = "ART_CLI_CodeArt")
    var aRTCLICodeArt: String = "",

    @SerialName("ART_CLI_CodeCli")
    @ColumnInfo(name = "ART_CLI_CodeCli")
    var aRTCLICodeCli: String = "",

    @SerialName("ART_TypePrix")
    @ColumnInfo(name = "ART_TypePrix")
    var aRTTypePrix: String? = null,

    @SerialName("ART_PrixClient")
    @ColumnInfo(name = "ART_PrixClient")
    var aRTPrixClient: String? = null,

    @SerialName("ART_export")
    @ColumnInfo(name = "ART_export")
    var aRTExport: String? = null,

    @SerialName("ART_DDm")
    @ColumnInfo(name = "ART_DDm")
    var aRTDDm: String? = null,

    @SerialName("ART_station")
    @ColumnInfo(name = "ART_station")
    var aRTStation: String? = null,

    @SerialName("ART_user")
    @ColumnInfo(name = "ART_user")
    var aRTUser: String? = null,

    @SerialName("ART_unite")
    @ColumnInfo(name = "ART_unite")
    var aRTUnite: String? = null,

    @SerialName("ART_TRemise")
    @ColumnInfo(name = "ART_TRemise")
    var aRTTRemise: String? = null
)
