package com.asmtunis.procaisseinventory.articles

import androidx.paging.PagingData
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import kotlinx.coroutines.flow.Flow


data class ArticlesListState(
    val lists: Flow<PagingData<Article>>? = null,
    val listOrder: ListOrder = ListOrder.Title(OrderType.Descending),
    val filter: ListSearch = ListSearch.FirstSearch(),
    val filterByFamille: String = "",
    val filterByMarque: String = "",
)