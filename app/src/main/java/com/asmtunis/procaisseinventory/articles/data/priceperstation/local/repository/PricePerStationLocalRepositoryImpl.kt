package com.asmtunis.procaisseinventory.articles.data.priceperstation.local.repository

import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import com.asmtunis.procaisseinventory.articles.data.priceperstation.local.dao.PricePerStationDAO
import kotlinx.coroutines.flow.Flow




class PricePerStationLocalRepositoryImpl(
    private val pricePerStationDAO: PricePerStationDAO
) : PricePerStationLocalRepository {

    override fun upsertAll(value: List<PricePerStation>) = pricePerStationDAO.insertAll(value)

    override fun deleteAll() = pricePerStationDAO.deleteAll()

    override fun getAll(): Flow<List<PricePerStation>> = pricePerStationDAO.all
    override fun getOneByArticle(article: String, station: String): Flow<PricePerStation?>
    = pricePerStationDAO.getOneByArticle(article, station)
}