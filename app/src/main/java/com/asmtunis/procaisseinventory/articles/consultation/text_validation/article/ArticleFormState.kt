package com.asmtunis.procaisseinventory.articles.consultation.text_validation.article

import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT


data class ArticleFormState(

    val designation: String = "",
    val designationError: UiText? = null,

    val barCode: String = "",
    val barCodeError: UiText? = null,

    val ref: String = "",
    val refError: UiText? = null,

    val prixHT: String = "",
    val prixHTError: UiText? = null,

    val prixTTC: String = "",
    val prixTTCError: UiText? = null,

    val qteTTStations: String = "",
    val qteTTStationsError: UiText? = null,

    val qteStation: String = "",
    val qteStationError: UiText? = null,

    val qteStock: String = "",
    val qteStockError: UiText? = null,

    val typeProduit: String = "",
    val typeProduitError: UiText? = null,

    val famille: Famille = Famille(),
    val familleError: UiText? = null,

    val marque: Marque = Marque(),
    val marqueError: UiText? = null,

    val typePrix: TypePrixUnitaireHT = TypePrixUnitaireHT(),
    val typePrixError: UiText? = null,

    val tva: Tva = Tva(),
    val tvaError: UiText? = null,



    val unite: Unite = Unite(),
    val uniteError: UiText? = null,
    )