package com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine

import android.content.Context
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.staggeredgrid.LazyStaggeredGridState
import androidx.compose.foundation.lazy.staggeredgrid.LazyVerticalStaggeredGrid
import androidx.compose.foundation.lazy.staggeredgrid.StaggeredGridCells
import androidx.compose.foundation.lazy.staggeredgrid.rememberLazyStaggeredGridState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.twotone.HideImage
import androidx.compose.material.icons.twotone.SaveAs
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.paging.LoadState
import androidx.paging.compose.LazyPagingItems
import androidx.paging.compose.collectAsLazyPagingItems
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.ArticleTextValidationViewModel
import com.asmtunis.procaisseinventory.articles.consultation.view_model.ArticlesViewModel
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.AUTRE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.enum_classes.StockArticle
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddArticlesRoute
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.SetNumSerieView
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControleInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.updateInvPatQty
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.bottom_sheet.AddMarqueBottomSheet
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BarCodeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.BareCode
import com.asmtunis.procaisseinventory.shared_ui_components.cameraview.barcode.openBareCodeScanner
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.ToasterState
import com.dokar.sonner.rememberToasterState
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import com.simapps.ui_kit.ModifiersUtils.roundedCornerShape
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import java.util.Locale


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SelectPatrimoineScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingViewModel: SettingViewModel,
    articleTxtValidViewModel: ArticleTextValidationViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    networkViewModel: NetworkViewModel,
    articlesViewModel: ArticlesViewModel,
    barCodeViewModel: BarCodeViewModel,
    mainViewModel: MainViewModel,
    dataViewModel: DataViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val invPatViewModel = proCaisseViewModels.invPatViewModel
    val batimentViewModel = proCaisseViewModels.batimentViewModel
    val selectPatrimoineVM = proCaisseViewModels.selectPatrimoineVM

    val context = LocalContext.current

   val imageList = mainViewModel.imageList
   val articleMapByBarCode = mainViewModel.articleMapByBarCode
    val listFamille = mainViewModel.listFamille
    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)

   val selectedPatrimoineList = selectPatrimoineVM.selectedPatrimoineList
    val marqueFilter = selectPatrimoineVM.marqueFilter
    val snackbarHostState = remember { SnackbarHostState() }
    val listState = rememberLazyStaggeredGridState()

    val articlesListState = articlesViewModel.articlesListState
    val articleList: LazyPagingItems<Article>? = articlesListState.lists?.collectAsLazyPagingItems()

    val listOrder = articlesListState.listOrder

    val listFilter = articlesListState.filter
    val filterList = context.resources.getStringArray(R.array.patimoine_filter)

    val marqueList = mainViewModel.marqueList
    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val canAddArticle = getProCaisseDataViewModel.authorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.ADD_ARTICLE}

    val selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine
    val barCodeInfo = barCodeViewModel.barCodeInfo

    val selectedMarque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque()

    val haveCamera = dataViewModel.getHaveCameraDevice()
    val selectedZoneConsomation = batimentViewModel.selectedZoneConsomation
    LaunchedEffect(key1 = Unit){
        //barCodeInfo.value.isNotEmpty()) then from scan dony hide dialogue
        if(barCodeInfo.value.isEmpty()) {
            selectPatrimoineVM.onShowSetNumeSerieChange(false)
        }


        articlesViewModel.onfilterByStockChange(StockArticle.PAS_FILTER_STOCK.filter)
    }
    LaunchedEffect(key1 = Unit
//        key1 = articlesViewModel.searchTextState.text,
//        key2 = articlesListState.lists,
//        key3 = articlesListState.filter
    ) {
        articlesViewModel.filterArticles(articlesListState = articlesListState)
    }
    LaunchedEffect(key1 = articlesViewModel.stockArticleStock) {
        if (articlesViewModel.stockArticleStock != StockArticle.SELECTED.filter) articlesViewModel.filterArticles(articlesListState = articlesListState)
    }




    val isVisible = floatingBtnIsVisible(
        listeSize = articleList?.itemCount?: 0,
        canScrollForward = listState.canScrollForward
    )





    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack()
                   // navController.navigate(Screen.ZoneConsomationDetailScreen.Route)
                },
                showNavIcon = !articlesViewModel.showSearchView && articlesViewModel.searchTextState.text.isEmpty(),
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.select_patrimoines),
                titleVisibilty = !articlesViewModel.showSearchView && articlesViewModel.searchTextState.text.isEmpty(),
                //  showNavIcom = clientViewModel.searchTextState.text.isBlank(),

                actions = {
                    SearchSectionComposable(
                        label = context.getString(
                            R.string.filter_by,
                            when (listFilter) {
                                is ListSearch.FirstSearch -> filterList.first()
                                is ListSearch.SecondSearch -> filterList[1]
                                else -> filterList[2]
                            }
                        ),
                        searchVisibility = articlesViewModel.showSearchView || articlesViewModel.searchTextState.text.isNotEmpty(),
                        // requestFocus = mainViewModel.selectedClient == Client(),
                        searchTextState = articlesViewModel.searchTextState,
                        onSearchValueChange = {
                            articlesViewModel.onSearchValueChange(TextFieldValue(it))
                        },
                        onShowSearchViewChange = {
                            articlesViewModel.onShowSearchViewChange(it)
                        },
                        onShowCustomFilterChange = {
                            articlesViewModel.onShowCustomFilterChange(it)
                        }
                    )
                }
            )
        },
        floatingActionButton = {

            val density = LocalDensity.current
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment  = Alignment.CenterHorizontally
            ) {
            AnimatedVisibility(
                visible = isVisible,
                enter = slideInVertically {
                    with(density) { 40.dp.roundToPx() }
                } + fadeIn(),
                exit = fadeOut(
                    animationSpec = keyframes {
                        this.durationMillis = 120
                    }
                )
            ) {
                FloatingActionButton(
                    onClick = {
                        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())
                        popBackStack()
                    }) {
                    BadgedBox(
                       // modifier = Modifier.size(100.dp).padding(top = 12.dp),
                        badge = {
                            Badge {
                                Text(text = selectedPatrimoineList.size.toString())
                            }
                        }
                    ) {
                        Icon(
                            imageVector = Icons.TwoTone.SaveAs,
                            contentDescription = stringResource(id = R.string.cd_achat_button)
                        )
                    }

                }
            }

            Spacer(modifier = Modifier.height(12.dp))
                AnimatedVisibility(
                    visible = isVisible && canAddArticle,
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {

                FloatingActionButton(
                    onClick = {
                        articlesViewModel.onCurrentArticleChange(Article())
                        articleTxtValidViewModel.resetVariable()
                        articlesViewModel.onModifyChange(true)
                        navigate(AddArticlesRoute)
                    }
                ) {
                    Icon(
                        imageVector = Icons.Default.Add,
                        contentDescription = stringResource(id = R.string.add_Article_button)
                    )
                }

                Spacer(modifier = Modifier.height(12.dp))
            }


                Spacer(modifier = Modifier.height(12.dp))
                AnimatedVisibility(
                    visible = isVisible,
                    enter = slideInVertically {
                        with(density) { 40.dp.roundToPx() }
                    } + fadeIn(),
                    exit = fadeOut(
                        animationSpec = keyframes {
                            this.durationMillis = 120
                        }
                    )
                ) {
                    SnapScrollingButton(
                        isScrollInProgress = listState.isScrollInProgress,
                        isVisible = listState.firstVisibleItemIndex > 15 && isVisible,
                        density = density,
                        animateScrollToItem = {
                            listState.animateScrollToItem(index = it)
                        }
                    )
                }

            }

        }


    ) { padding ->

        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {

            if(mainViewModel.addNewMarqueIsVisible) {

                AddMarqueBottomSheet(
                    toaster = toaster,
                    marqueTxt = selectPatrimoineVM.marqueTxt,
                    onMarqueTextChange = {
                        selectPatrimoineVM.onMarqueTextChange(it)
                    },
                setVisibilty = {
                    mainViewModel.onAddNewMarqueVisibilityChange(it)
                },
                onSaveClick = {
                    mainViewModel.addNewMarque(
                        designationMarque = selectPatrimoineVM.marqueTxt,
                        onSelectedMarqueChange = {
                            selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(marqueCode = it.mARCode))
                        }
                    )
                    selectPatrimoineVM.onMarqueTextChange("")


                }
                )
            }

            if (selectPatrimoineVM.showSetNumeSerie) {
                SetNumSerieView(
                    articleMapByBarCode = articleMapByBarCode,
                    marqueList = marqueList,
                    haveCamera = haveCamera,
                    selectedPatrimoine = selectedPatrimoine,
                    selectedPatrimoineList = selectedPatrimoineList,
                    onNumSerieChange = {
                        selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(numSerie = it))
                    },
                    onDismiss = {
                        barCodeViewModel.onBarCodeInfo(barCode = BareCode())
                        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())

                        selectPatrimoineVM.resetPatrimoineVerificationState()

                        selectPatrimoineVM.onShowSetNumeSerieChange(false)
                    },
                    onConfirm = {
                        val controlInvPat = ControleInventaire(
                            LG_DEV_NumSerie = selectedPatrimoine.numSerie,
                            //TODO SEEN TO DELETE CLIENTS TABLE FROM INV PAT AND IMMO
                            DEV_CodeClient = mainViewModel.clientByCode.cLICode.ifEmpty { batimentViewModel.selectedZoneConsomation.cLICode },
                            DEV_info3 = invPatViewModel.typeInvetaireState
                        )
                        selectPatrimoineVM.patrimoineVerification(
                            baseConfig = mainViewModel.selectedBaseconfig,
                            controlPatrimoine = controlInvPat
                        )

                        selectPatrimoineVM.onKeepTypedNumSerieChange(false)
                    },
                    onAddInvPat ={
                        updateInvPatQty(
                            imageList = imageList,
                            articleCode = selectPatrimoineVM.selectedPatrimoine.articleCode,
                            numeSerie = selectedPatrimoine.numSerie,
                            patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState,
                            selectedPatrimoineList =  selectedPatrimoineList,
                            addItemToSelectedPatrimoineList = {
                                selectPatrimoineVM.addItemToSelectedPatrimoineList(it)
                            },
                            marque = marqueList.firstOrNull { it.mARCode == selectedPatrimoine.marqueCode }?: Marque(),
                            note = selectedPatrimoine.note
                        )

                    },
                    onBareCodeScan = {
                        openBareCodeScanner(
                            navigate = { navigate(it) },
                            onBarCodeInfo = {
                                barCodeViewModel.onBarCodeInfo(
                                barCode = it
                                )
                            }
                        )
                    },
                    barCodeInfo = barCodeInfo,
                    patrimoineVerificationState = selectPatrimoineVM.patrimoineVerificationState,
                    showDropDownMenuComposable = true,
                    dropDownMenuComposable = {

                        LaunchedEffect(key1 = Unit) {
                            if(marqueList.isNotEmpty()) {
                                val marq = marqueList.firstOrNull { it.mARCode == "00" }?: marqueList.first()
                                selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(marqueCode = marq.mARCode))
                            }
                        }
                        GenericDropdownMenu(
                            modifier = Modifier.fillMaxWidth(0.7f),
                            label = stringResource(R.string.marque_title),
                            fiterValue = marqueFilter,
                           onFilterValueChange = { selectPatrimoineVM.onMarqueFilterChange(it)},
                           showFilter = true,
                            designation = selectedMarque.mARDesignation,
                            itemList = if(marqueFilter.isNotEmpty()) marqueList.filter { it.mARDesignation.lowercase(Locale.ROOT).contains(marqueFilter.lowercase(Locale.ROOT)) } else marqueList,
                            itemExpanded = invPatViewModel.marqueExpanded,
                            selectedItem = selectedMarque,
                            onItemExpandedChange = {
                                invPatViewModel.onMarqueExpandedChange(it)
                            },
                            getItemDesignation = { it.mARDesignation },
                            getItemSyncStatus = { it.isSync },
                            getItemTrailing = { it.mARCode },
                            onClick = {

                                if(it.mARCode == AUTRE) {
                                   // selectPatrimoineVM.onShowSetNumeSerieChange(false)

                                    mainViewModel.generteNewMarqueCode()
                                    mainViewModel.onAddNewMarqueVisibilityChange(true)
                                }
                                else selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(marqueCode = it.mARCode))
                            },
                            lottieAnimEmpty = {
                                LottieAnim(lotti = R.raw.emptystate)
                            },
                            lottieAnimError = {
                                LottieAnim(lotti = R.raw.connection_error, size = it)
                            }
                        )
                    },
                    onNoteChange = {
                        selectPatrimoineVM.setSelectedPat(selectedPatrimoine.copy(note = it))
                    }
                )
            }

            if (articlesViewModel.showCustomFilter) {
                FilterContainer(
                    filterList = filterList,
                    listFilter = listFilter,
                    listOrder = listOrder,
                    orderList = context.resources.getStringArray(R.array.patimoine_filter),
                    onShowCustomFilterChange = { articlesViewModel.onShowCustomFilterChange(false) },
                    onEvent = { articlesViewModel.onEvent(event = it) },
                    customFilterContent = {
                        FilterSectionComposable(
                            title = stringResource(R.string.filter_by_famille),
                            currentFilterLable = articlesListState.filterByFamille,
                            onAllEvent = { articlesViewModel.onEvent(event = ListEvent.FirstCustomFilter("")) },
                            onEvent = { articlesViewModel.onEvent(event = ListEvent.FirstCustomFilter(listFamille[it].fAMCode)) },
                            filterCount = listFamille.size,
                            customFilterCode = { listFamille[it].fAMCode },
                            filterLabel = { listFamille[it].fAMLib?: listFamille[it].fAMDesgCourte }
                        )


                        FilterSectionComposable(
                            title = stringResource(R.string.filter_by_marque),
                            currentFilterLable = articlesListState.filterByMarque,
                            onAllEvent = { articlesViewModel.onEvent(event = ListEvent.SecondCustomFilter("")) },
                            onEvent = { articlesViewModel.onEvent(event = ListEvent.SecondCustomFilter(marqueList[it].mARDesignation)) },
                            filterCount = marqueList.size,
                            customFilterCode = { marqueList[it].mARDesignation },
                            filterLabel = { marqueList[it].mARDesignation }
                        )
                    }
                )
            }
            articleList?.apply {
                when {
                    loadState.refresh is LoadState.Loading -> {
                        // Show a loading indicator
                        CircularProgressIndicator()
                    }
                    loadState.append is LoadState.Loading -> {
                        //Show loading next page indicator
                        //  CircularProgressIndicator()
                    }
                    loadState.refresh is LoadState.Error -> {
                        //Show a Error
                        val e = loadState.refresh as LoadState.Error

                        Text(
                            text = e.error.localizedMessage!!,
                            modifier = Modifier.padding(8.dp)
                        )


                    }
                    loadState.append is LoadState.Error -> {
                        //Show a Error
                        val e = loadState.append as LoadState.Error

                        Text(
                            text = e.error.localizedMessage!!,
                            modifier = Modifier.padding(8.dp)
                        )

                    }
                }
            }
            if ((articleList?.itemCount?: 0) > 0) {
                SelectArticlesList(
                    listState = listState,
                    filteredArt = articleList,
                    selectedPatrimoineList = selectedPatrimoineList,
                    onClick = {
                       /* if(!it.isSync) {

                            showToast(
                                context = context,
                                toaster = toaster,
                                message = context.resources.getString(R.string.syncbefore_use_art),
                                type =  ToastType.Info,
                            )
                            return@SelectArticlesList
                        }*/
                        barCodeViewModel.onBarCodeInfo(barCode = BareCode())

                        if(!selectPatrimoineVM.keepTypedNumSerie){
                            selectPatrimoineVM.setSelectedPat(SelectedPatrimoine())

                        }

                        selectPatrimoineVM.onShowSetNumeSerieChange(true)

                        selectPatrimoineVM.setSelectedPat(SelectedPatrimoine(articleCode = it.aRTCode, numSerie = if(selectPatrimoineVM.keepTypedNumSerie) selectedPatrimoine.numSerie else ""))
                    }
                )
            }
            else LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
        }
    }

}

@Composable
fun SelectArticlesList(
    listState: LazyStaggeredGridState,
    filteredArt: LazyPagingItems<Article>?,
    onClick :(Article)->Unit,
    selectedPatrimoineList : List<SelectedPatrimoine>
) {
    if(filteredArt == null) return

    val filteredArticles = filteredArt.itemSnapshotList.sortedWith(compareByDescending { art->
        selectedPatrimoineList.find { pat-> pat.articleCode == art?.aRTCode }?.quantity ?: 0.0
    })

    LazyVerticalStaggeredGrid(
       contentPadding = PaddingValues(12.dp),
       // columns = GridCells.Adaptive(minSize = 128.dp)

        columns = StaggeredGridCells.Adaptive(minSize = 128.dp),
        state = listState,
        horizontalArrangement = Arrangement.spacedBy(16.dp),
        verticalItemSpacing = 16.dp,
    ) {
        items(
            count = filteredArticles.size,
            key = {
                filteredArticles[it]?.aRTCode?: ""
            }
        ) { index ->
            val article = filteredArticles[index]!!
            val quantity = selectedPatrimoineList.filter { it.articleCode == article.aRTCode}.size

                OutlinedCard(
                    modifier = Modifier.clickable {

                        onClick(article)
                    },
                    shape = roundedCornerShape(),

                ) {
                    Column(
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .wrapContentHeight()
                            .fillMaxWidth()
                            .padding(12.dp),


                    ) {
                        // Spacer(modifier = Modifier.height(12.dp))
                        Text(
                            modifier = Modifier.fillMaxWidth(),
                            textAlign = TextAlign.Center,
                            text = article.aRTDesignation,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.SemiBold,
                            maxLines = 3,
                        )
                        if (quantity > 0.0)
                            BadgedBox(
                                modifier = Modifier.size(100.dp).padding(top = 12.dp),
                                badge = {
                                Badge {
                                    Text(text = quantity.toString())
                                }
                            }
                            ) {
                                ImageSection(filteredArticle = article, onClick = { onClick(it) })
                            }
                        else  Box(
                            modifier = Modifier.size(100.dp),
                            contentAlignment = Alignment.Center
                        ){
                            ImageSection(filteredArticle = article, onClick = { onClick(it) })
                        }

                        Text(
                            modifier = Modifier.fillMaxWidth(),
                            textAlign = TextAlign.Center,
                            text = article.aRTCodeBar,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            maxLines = 3,
                        )






                    }
                }

           // }


          //  Divider(color = MaterialTheme.colorScheme.tertiary)
        }
    }
}

fun selectedArticle(
    listSelectedPatrimoine: List<SelectedPatrimoine>,
    articleCode: String
): SelectedPatrimoine = listSelectedPatrimoine.firstOrNull { it.articleCode == articleCode }?: SelectedPatrimoine()





fun setLigneArticle(
    selectedPatrimoine: SelectedPatrimoine
): SelectedPatrimoine {


    val quantity = if (selectedPatrimoine.quantity == 0.0) 1.0
    else selectedPatrimoine.quantity
    return SelectedPatrimoine(
        articleCode = selectedPatrimoine.articleCode,
        quantity = quantity
    )
}


fun addSelectedPatrimoine(
    context: Context,
    toaster: ToasterState,
    fromScan: Boolean = true,

    barCodeViewModel: BarCodeViewModel,
    selectPatrimoineVM: SelectPatrimoineViewModel
) {

    val selectedArticle = selectPatrimoineVM.selectedPatrimoine

    val addedArtcl: SelectedPatrimoine


    if (fromScan) {
        val quantity = if (selectedArticle.quantity != 0.0) selectedArticle.quantity + 1
        else selectedArticle.quantity



        selectPatrimoineVM.setSelectedPat(
            selectedArticle.copy(
                quantity = quantity
            )
        )
        addedArtcl = setLigneArticle(selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine)


    }
    else addedArtcl = setLigneArticle(selectedPatrimoine = selectPatrimoineVM.selectedPatrimoine)



    selectPatrimoineVM.addItemToSelectedPatrimoineList(addedArtcl)

//    showToast(
//        context = context,
//        toaster = toaster,
//        message = context.resources.getString(R.string.ajout) + "\n"+ selectedArticle.article.aRTDesignation,
//        type =  ToastType.Success,
//    )
    barCodeViewModel.onBarCodeInfo(barCode = BareCode())
}




@Composable
fun ImageSection(filteredArticle: Article,  onClick :(Article)->Unit,) {
    if (!filteredArticle.isSync) {
        LottieAnim(lotti = R.raw.connection_error, size = 80.dp, onClick = { onClick(filteredArticle) })
    } else Icon(
        imageVector = Icons.TwoTone.HideImage,
        contentDescription = "",

        modifier = Modifier.size(80.dp)
    )
}







