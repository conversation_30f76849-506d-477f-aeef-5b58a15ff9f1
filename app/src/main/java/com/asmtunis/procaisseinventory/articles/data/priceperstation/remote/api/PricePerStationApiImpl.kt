package com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.api

import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class PricePerStationApiImpl(private val client: HttpClient) : PricePerStationApi {
    override suspend fun getPricesByStation(baseConfig: String): Flow<DataResult<List<PricePerStation>>> =
        flow {
            val resutl = executePostApiCall<List<PricePerStation>>(
                client = client,
                endpoint = Urls.GET_PRICES_BY_STATION,
                baseConfig = baseConfig
            )
            emitAll(resutl)
        }
}