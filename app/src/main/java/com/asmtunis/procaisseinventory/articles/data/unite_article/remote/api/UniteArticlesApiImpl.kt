package com.asmtunis.procaisseinventory.articles.data.unite_article.remote.api

import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.PaginationResponseUniteArticle
import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class UniteArticlesApiImpl(private val client: HttpClient) : UniteArticlesApi {
    override suspend fun getUniteArticlesPagination(
        baseConfig: String,
        page: String,
        limit: String
    ): Flow<DataResult<PaginationResponseUniteArticle?>>  = flow {
        val queryParams = mapOf(
            "page" to page,
            "limit" to limit
        )

        val result = executePostApiCall<PaginationResponseUniteArticle?>(
            client = client,
            endpoint = Urls.GET_UNITE_ARTICLE_PAGINATION,
            queryParams = queryParams,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

}