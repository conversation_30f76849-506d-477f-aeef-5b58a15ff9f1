package com.asmtunis.procaisseinventory.articles.data.article.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.CLIENTS_ARTICLE_PRIX_TABLE
import kotlinx.coroutines.flow.Flow

@Dao
interface ClientArticlePrixDAO {

    @get:Query("SELECT * FROM $CLIENTS_ARTICLE_PRIX_TABLE")
    val all: Flow<List<ClientArticlePrix>>

    @Query("SELECT * FROM $CLIENTS_ARTICLE_PRIX_TABLE where ART_CLI_CodeArt = :artCliCodeArt AND ART_CLI_CodeCli = :artCliCodeCli")
    fun getByClientAndProduct(artCliCodeArt: String?, artCliCodeCli: String?): ClientArticlePrix?

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: ClientArticlePrix)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<ClientArticlePrix>)

    @Query("DELETE FROM $CLIENTS_ARTICLE_PRIX_TABLE")
    fun deleteAll()
}
