package com.asmtunis.procaisseinventory.articles.consultation.screens

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.consumeWindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.enum_classes.StockArticle

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StockFilterSegmentedButton(
    onfilterByStockChange: (String) -> Unit,
    stockArticleStock: String
) {
    SingleChoiceSegmentedButtonRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 12.dp, end = 12.dp)
            .consumeWindowInsets(TopAppBarDefaults.windowInsets)
    ) {
        SegmentedButton(
            shape = SegmentedButtonDefaults.itemShape(index = 0, count = 3),
            onClick = {  onfilterByStockChange(StockArticle.PAS_FILTER_STOCK.filter) },
            selected = stockArticleStock == StockArticle.PAS_FILTER_STOCK.filter
        ) {
            Text(
                modifier = Modifier.horizontalScroll(rememberScrollState()),
                text= stringResource(R.string.all_label_f),
                style = MaterialTheme.typography.bodySmall,
                softWrap = false,
                maxLines = 1
            )
        }

        SegmentedButton(
            shape = SegmentedButtonDefaults.itemShape(index = 1, count = 3),
            onClick = { onfilterByStockChange(StockArticle.EN_STOCK.filter) },
            selected = stockArticleStock == StockArticle.EN_STOCK.filter
        ) {
            Text(
                modifier = Modifier.horizontalScroll(rememberScrollState()),
                text= stringResource(R.string.en_stock),
                style = MaterialTheme.typography.bodySmall,
                softWrap = false,
                maxLines = 1
            )
        }

        SegmentedButton(
            shape = SegmentedButtonDefaults.itemShape(index = 2, count = 3),
            onClick = { onfilterByStockChange(StockArticle.HORS_STOCK.filter) },
            selected = stockArticleStock == StockArticle.HORS_STOCK.filter
        ) {
            Text(
                modifier = Modifier.horizontalScroll(rememberScrollState()),
                text = stringResource(R.string.hors_stock),
                style = MaterialTheme.typography.bodySmall,
                softWrap = false,
                maxLines = 1
            )
        }
    }
}

