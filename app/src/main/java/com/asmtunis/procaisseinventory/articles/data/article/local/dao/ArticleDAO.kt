package com.asmtunis.procaisseinventory.articles.data.article.local.dao

import androidx.paging.PagingSource
import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants.Companion.Articles_TABLE
import kotlinx.coroutines.flow.Flow

//to do verify wether to use ART_QteStock or SART_Qte
@Dao
interface ArticleDAO {
    @get:Query("SELECT * FROM $Articles_TABLE")
    val all: Flow<List<Article>?>


    @Query("UPDATE $Articles_TABLE SET ART_QteStock =:newQteAllStations, ART_QteStock =:newQteStation  WHERE  ART_Code=:codeArticle ")
    fun updateArtQteStock(newQteAllStations: String, newQteStation : String, codeArticle: String)

    @Transaction
    @Query(
        "SELECT * FROM $Articles_TABLE " +
            "WHERE ART_Designation LIKE '%' || :filterString || '%'" +
                "and  CASE WHEN :filterByFamille !=  '' THEN FAM_Lib=:filterByFamille ELSE FAM_Lib !=:filterByFamille  END " +
                "and  CASE WHEN :filterByMarque !=  '' THEN MAR_Designation =:filterByMarque ELSE MAR_Designation !=:filterByMarque  END " +

                "AND ( " +
                "    (:stock = 'ENSTOCK' AND ART_QteStock > 0) " +
                "    OR (:stock = 'HORSTOCK' AND ART_QteStock <= 0) " +
                "    OR (:stock = 'ALLSTOCK') " +
                "   " +
                ") " +
            " ORDER BY " +
                "CASE WHEN :sortBy = 'cART_Designation'  AND :isAsc = 1 THEN ART_Designation END ASC, " +
                "CASE WHEN :sortBy = 'ART_Designation'  AND :isAsc = 2 THEN ART_Designation END DESC, " +
                "CASE WHEN :sortBy = 'ddm'  AND :isAsc = 1 THEN ddm END ASC, " +
                "CASE WHEN :sortBy = 'ddm'  AND :isAsc = 2 THEN ddm END DESC, " +
                "CASE WHEN :sortBy = 'pvttc'  AND :isAsc = 1 THEN pvttc END ASC, " +
                "CASE WHEN :sortBy = 'pvttc'  AND :isAsc = 2 THEN pvttc END DESC "
    )
    fun filterByName(filterString: String, sortBy: String?, stock: String?, isAsc: Int?, filterByFamille: String, filterByMarque: String): PagingSource<Int, Article>

    @Transaction
    @Query(
        "SELECT * FROM $Articles_TABLE " +
            "WHERE ART_CodeBar LIKE '%' || :filterString || '%'" +
                "and  CASE WHEN :filterByFamille !=  '' THEN FAM_Lib=:filterByFamille ELSE FAM_Lib !=:filterByFamille  END " +
                "and  CASE WHEN :filterByMarque !=  '' THEN MAR_Designation =:filterByMarque ELSE MAR_Designation !=:filterByMarque  END " +

                "AND ( " +
                "    (:stock = 'ENSTOCK' AND ART_QteStock > 0) " +
                "    OR (:stock = 'HORSTOCK' AND ART_QteStock <= 0) " +
                "    OR (:stock = 'ALLSTOCK') " +
                "   " +
                ") " +
            " ORDER BY " +

                "CASE WHEN :sortBy = 'ART_Designation'  AND :isAsc = 1 THEN ART_Designation END ASC, " +
                "CASE WHEN :sortBy = 'ART_Designation'  AND :isAsc = 2 THEN ART_Designation END DESC, " +
                "CASE WHEN :sortBy = 'ddm'  AND :isAsc = 1 THEN ddm END ASC, " +
                "CASE WHEN :sortBy = 'ddm'  AND :isAsc = 2 THEN ddm END DESC, " +
                "CASE WHEN :sortBy = 'pvttc'  AND :isAsc = 1 THEN pvttc END ASC, " +
                "CASE WHEN :sortBy = 'pvttc'  AND :isAsc = 2 THEN pvttc END DESC "
    )
    fun filterByBarCode(filterString: String, sortBy: String?, stock: String?, isAsc: Int?, filterByFamille: String, filterByMarque: String): PagingSource<Int, Article>

    @Transaction
    @Query(
        "SELECT * FROM $Articles_TABLE " +
            "WHERE pvttc LIKE '%' || :filterString || '%' " +
                "and  CASE WHEN :filterByFamille !=  '' THEN FAM_Lib=:filterByFamille ELSE FAM_Lib !=:filterByFamille  END " +
                "and  CASE WHEN :filterByMarque !=  '' THEN MAR_Designation =:filterByMarque ELSE MAR_Designation !=:filterByMarque  END " +

                "AND ( " +
                "    (:stock = 'ENSTOCK' AND ART_QteStock > 0) " +
                "    OR (:stock = 'HORSTOCK' AND ART_QteStock <= 0) " +
                "    OR (:stock = 'ALLSTOCK') " +
                "   " +
                ") " +

            " ORDER BY " +

                "CASE WHEN :sortBy = 'ART_Designation'  AND :isAsc = 1 THEN ART_Designation END ASC, " +
                "CASE WHEN :sortBy = 'ART_Designation'  AND :isAsc = 2 THEN ART_Designation END DESC, " +
                "CASE WHEN :sortBy = 'ddm'  AND :isAsc = 1 THEN ddm END ASC, " +
                "CASE WHEN :sortBy = 'ddm'  AND :isAsc = 2 THEN ddm END DESC, " +
                "CASE WHEN :sortBy = 'pvttc'  AND :isAsc = 1 THEN pvttc END ASC, " +
                "CASE WHEN :sortBy = 'pvttc'  AND :isAsc = 2 THEN pvttc END DESC "
    )
    fun filterByPrice(filterString: String, sortBy: String?, stock: String?, isAsc: Int?, filterByFamille: String, filterByMarque: String): PagingSource<Int, Article>



//    @Transaction
//    @Query(
//        "SELECT * FROM $Articles_TABLE " +
//                "LEFT JOIN $STATION_ARTICLE_TABLE ON $Articles_TABLE.ART_Code = $STATION_ARTICLE_TABLE.SART_CodeArt " +
//                "WHERE " +
//                "CASE WHEN :filterByFamille != '' THEN FAM_Lib = :filterByFamille ELSE 1=1 END " +
//                "AND CASE WHEN :filterByMarque != '' THEN MAR_Designation = :filterByMarque ELSE 1=1 END " +
//                "AND ( " +
//                // When station is provided, check station stock
//                "  (:station != '' AND ( " +
//                "    (:stock = 'ENSTOCK' AND $STATION_ARTICLE_TABLE.SART_Qte_Station > 0 AND $STATION_ARTICLE_TABLE.SART_CodeSatation = :station) " +
//                "    OR (:stock = 'HORSTOCK' AND $STATION_ARTICLE_TABLE.SART_Qte_Station <= 0 AND $STATION_ARTICLE_TABLE.SART_CodeSatation = :station) " +
//                "    OR (:stock = 'ALLSTOCK' AND $STATION_ARTICLE_TABLE.SART_CodeSatation = :station) " +
//                "  )) " +
//                // When station is empty, check main stock
//                "  OR (:station = '' AND ( " +
//                "    (:stock = 'ENSTOCK' AND ART_QteStock > 0) " +
//                "    OR (:stock = 'HORSTOCK' AND ART_QteStock <= 0) " +
//                "    OR (:stock = 'ALLSTOCK') " +
//                "  )) " +
//                ") " +
//                "ORDER BY " +
//                "CASE WHEN :sortBy = 'ART_Designation' AND :isAsc = 1 THEN ART_Designation END ASC, " +
//                "CASE WHEN :sortBy = 'ART_Designation' AND :isAsc = 2 THEN ART_Designation END DESC, " +
//                "CASE WHEN :sortBy = 'ddm' AND :isAsc = 1 THEN ddm END ASC, " +
//                "CASE WHEN :sortBy = 'ddm' AND :isAsc = 2 THEN ddm END DESC, " +
//                "CASE WHEN :sortBy = 'pvttc' AND :isAsc = 1 THEN pvttc END ASC, " +
//                "CASE WHEN :sortBy = 'pvttc' AND :isAsc = 2 THEN pvttc END DESC "
//    )
//    fun getAllFiltred(sortBy: String?, stock: String?, isAsc: Int?, filterByFamille: String, filterByMarque: String, station: String?): Flow<Map<Article, List<StationStockArticle>>>.


    @Query(
        "SELECT * FROM $Articles_TABLE " +
                "WHERE " +
                "(:filterByFamille = '' OR FAM_Lib = :filterByFamille) " +
                "AND (:filterByMarque = '' OR MAR_Designation = :filterByMarque) " +
                "AND ( " +
                "    (:stock = 'ENSTOCK' AND ART_QteStock > 0) " +
                "    OR (:stock = 'HORSTOCK' AND ART_QteStock <= 0) " +
                "    OR (:stock = 'ALLSTOCK') " +
                "   " +
                ") " +
                "ORDER BY " +
                "CASE WHEN :sortBy = 'ART_Designation' AND :isAsc = 1 THEN ART_Designation END ASC, " +
                "CASE WHEN :sortBy = 'ART_Designation' AND :isAsc = 2 THEN ART_Designation END DESC, " +
                "CASE WHEN :sortBy = 'ddm' AND :isAsc = 1 THEN ddm END ASC, " +
                "CASE WHEN :sortBy = 'ddm' AND :isAsc = 2 THEN ddm END DESC, " +
                "CASE WHEN :sortBy = 'pvttc' AND :isAsc = 1 THEN pvttc END ASC, " +
                "CASE WHEN :sortBy = 'pvttc' AND :isAsc = 2 THEN pvttc END DESC "
    )
    fun getAllFiltred(sortBy: String?, stock: String?, isAsc: Int?, filterByFamille: String, filterByMarque: String): PagingSource<Int, Article>

    @get:Query("SELECT * FROM $Articles_TABLE WHERE Type_Produit not like '%patrimoine%' and ART_QteStock>0")
    val allNotPatStockable: Flow<List<Article?>?>?

    @get:Query("SELECT * FROM $Articles_TABLE WHERE Type_Produit not like '%patrimoine%'")
    val allNotPat: Flow<List<Article?>?>?

    @get:Query("SELECT * FROM $Articles_TABLE WHERE Type_Produit like '%patrimoine%'")
    val allPat: Flow<List<Article?>?>?

    // @Query("SELECT count(*) FROM Article WHERE SART_CodeSatation=:station")
    @get:Query("SELECT count(*) FROM $Articles_TABLE")
    val allCount: Flow<Int>

    @Query("SELECT count(*) FROM $Articles_TABLE WHERE SART_CodeSatation=:station and ART_QteStock>0")
    fun getAllCountGreaterThanZero(station: String?): Flow<Int?>?

    @get:Query("SELECT * FROM $Articles_TABLE")
    val allMutable: Flow<List<Article?>?>?

    @Query("SELECT * FROM $Articles_TABLE WHERE SART_CodeSatation=:station")
    fun getAllByStation(station: String?): List<Article?>?

    @Query("SELECT * FROM $Articles_TABLE WHERE SART_CodeSatation=:station and ART_QteStock>0")
    fun getAllByStationGreaterThanZero(station: String?): List<Article?>?

    @Query("SELECT * FROM $Articles_TABLE WHERE ART_Code = :code or ART_CodeBar = :code")
    fun getOneByCode(code: String): Flow<Article?>

    @Query("SELECT * FROM $Articles_TABLE WHERE ART_Code = :code and SART_CodeSatation = :station ")
    fun getOneByCodeAndStation(code: String?, station: String?): Article?



    @Query("SELECT * FROM $Articles_TABLE WHERE SART_CodeSatation=:station and ART_QteStock>0")
    fun getAllByStationGreaterThanZeroMutable(station: String?): Flow<List<Article?>?>?

    @Query("SELECT * FROM $Articles_TABLE WHERE SART_CodeSatation=:station and ART_QteStock>0 and Type_Produit not like '%patrimoine%'")
    fun getAllByStationGreaterThanZeroMutableNotPat(station: String?): Flow<List<Article?>?>?

    @Query("SELECT * FROM $Articles_TABLE WHERE SART_CodeSatation=:station and Type_Produit not like '%patrimoine%'")
    fun getAllByStationMutableNotPat(station: String?): Flow<List<Article?>?>?

    @Query("SELECT * FROM $Articles_TABLE WHERE ART_CodeBar LIKE :code or Fils_CodeBar LIKE :code ")
    fun getByCodeBar(code: String?): Article?

    @Query("SELECT * FROM $Articles_TABLE WHERE (ART_CodeBar LIKE :code or Fils_CodeBar LIKE :code or ART_Code LIKE :code) and SART_CodeSatation= :station")
    fun getByCodeBarAndSation(code: String?, station: String?): Article?

    @Query("SELECT * FROM $Articles_TABLE WHERE photo_Path = :code")
    fun getByNumSerie(code: String?): Article?

    @Query("SELECT COUNT(*) FROM $Articles_TABLE")
    fun count(): Int
    @get:Query("SELECT * FROM $Articles_TABLE WHERE isSync=0  and  (Status='INSERTED'  or Status='UPDATED')")
    val nonSync: Flow<List<Article>?>

    @Query("UPDATE $Articles_TABLE SET isSync = 1, Status= 'SELECTED' where ART_Code = :artCode")
    fun updateSyncArticle(artCode : String)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: Article)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<Article>)

    @Query("DELETE FROM $Articles_TABLE")
    fun deleteAll()

    @Query("DELETE FROM $Articles_TABLE where ART_Code = :aRTCode")
    fun deleteByArtCode(aRTCode: String)

    @get:Query("SELECT strftime('%Y-%m-%d %H-%M',ddm) FROM $Articles_TABLE order by strftime('%Y-%m-%d %H-%M',ddm) desc limit 1")
    val dDM: String?
}
