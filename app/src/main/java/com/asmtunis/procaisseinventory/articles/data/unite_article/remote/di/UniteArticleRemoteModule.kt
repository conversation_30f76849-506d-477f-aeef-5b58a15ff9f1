package com.asmtunis.procaisseinventory.articles.data.unite_article.remote.di

import com.asmtunis.procaisseinventory.articles.data.unite_article.remote.api.UniteArticlesApi
import com.asmtunis.procaisseinventory.articles.data.unite_article.remote.api.UniteArticlesApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object UniteArticleRemoteModule {


    @Provides
    @Singleton
    fun provideUniteArticlesApi(client: HttpClient): UniteArticlesApi = UniteArticlesApiImpl(client)


}