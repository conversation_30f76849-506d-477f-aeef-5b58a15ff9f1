package com.asmtunis.procaisseinventory.articles.data.priceperstation.local.repository

import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import kotlinx.coroutines.flow.Flow




interface PricePerStationLocalRepository {


    fun upsertAll(value: List<PricePerStation>)


    fun deleteAll()

    fun getAll(): Flow<List<PricePerStation>>
    fun getOneByArticle(article: String, station: String): Flow<PricePerStation?>

}