package com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille

import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.data.station.domaine.Station


data class FamilleFormState(

    val designationFamille: String = "",
    val designationFamilleError: UiText? = null,

    val designationFamilleCourte: String = "",
    val designationFamilleCourteError: UiText? = null,

    val ordre: String = "",
    val ordreError: UiText? = null,

    val station: Station = Station(),
    val stationError: UiText? = null,
    )