package com.asmtunis.procaisseinventory.articles.data.unite_article.remote.api

import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.PaginationResponseUniteArticle
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow

interface UniteArticlesApi {
    suspend fun getUniteArticlesPagination(baseConfig: String, page: String, limit: String): Flow<DataResult<PaginationResponseUniteArticle?>>

}