package com.asmtunis.procaisseinventory.articles.data.article.local.repository.article_codebar

import com.asmtunis.procaisseinventory.articles.data.article.domaine.ArticleCodeBar
import kotlinx.coroutines.flow.Flow



    interface ArticleBarCodeLocalRepository {

        fun upsert(value: ArticleCodeBar)

        fun upsertAll(value: List<ArticleCodeBar>)


        fun deleteAll()

        fun getAll(): Flow<List<ArticleCodeBar>>

}