package com.asmtunis.procaisseinventory.articles.data.priceperstation.local.di

import com.asmtunis.procaisseinventory.articles.data.priceperstation.local.dao.PricePerStationDAO
import com.asmtunis.procaisseinventory.articles.data.priceperstation.local.repository.PricePerStationLocalRepository
import com.asmtunis.procaisseinventory.articles.data.priceperstation.local.repository.PricePerStationLocalRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



@Module
@InstallIn(SingletonComponent::class)
class PricePerStationAppModule {

    @Provides
    @Singleton
    fun providePricePerStationDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.pricePerStationDAO()

    @Provides
    @Singleton
    @Named("PricePerStation")
    fun providePricePerStationRepository(
        pricePerStationDAO: PricePerStationDAO
    ): PricePerStationLocalRepository = PricePerStationLocalRepositoryImpl(
        pricePerStationDAO = pricePerStationDAO

    )


}
