package com.asmtunis.procaisseinventory.articles.data.unite_article.local.di

import com.asmtunis.procaisseinventory.articles.data.unite_article.local.dao.UniteArticleDAO
import com.asmtunis.procaisseinventory.articles.data.unite_article.local.repository.UniteArticleLocalRepository
import com.asmtunis.procaisseinventory.articles.data.unite_article.local.repository.UniteArticleLocalRepositoryImpl
import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class UniteArticleModule {

    @Provides
    @Singleton
    fun provideUniteArticlesDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.uniteArticleDAO()

    @Provides
    @Singleton
    @Named("UniteArticles")
    fun provideUniteArticlesRepository(
        uniteArticleDAO: UniteArticleDAO
    ): UniteArticleLocalRepository = UniteArticleLocalRepositoryImpl(
        uniteArticleDAO = uniteArticleDAO

    )


}
