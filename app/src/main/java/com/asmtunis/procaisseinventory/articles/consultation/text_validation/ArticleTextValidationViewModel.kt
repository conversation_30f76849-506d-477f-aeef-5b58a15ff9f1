package com.asmtunis.procaisseinventory.articles.consultation.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.article.ArticleFormEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.article.ArticleFormState
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille.FamilleFormEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.famille.FamilleFormState
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.marque.MarqueFormEvent
import com.asmtunis.procaisseinventory.articles.consultation.text_validation.marque.MarqueFormState
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateCanConvertStringToInt
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateDoubleNotZero
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.data.famille.domaine.Famille
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.data.station.domaine.Station
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.data.unite.domaine.Unite
import com.asmtunis.procaisseinventory.pro_inventory.data.type_prix_unitaire.domaine.TypePrixUnitaireHT
import kotlinx.coroutines.launch


class ArticleTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validateCanConvertStringToInt: ValidateCanConvertStringToInt = ValidateCanConvertStringToInt(),
    private val validateDoubleNotZero: ValidateDoubleNotZero = ValidateDoubleNotZero()
) : ViewModel() {

   // private val validationEventChannel = Channel<ValidationArticleEvent>()
   // val validationAddArticleEvents = validationEventChannel.receiveAsFlow()
    var validationAddArticleEvents by mutableStateOf(ValidationArticleEvent())


    fun onValidationAddArticleEventsChange(value: ValidationArticleEvent) {
        validationAddArticleEvents = value
    }

    /**
     * Add Article input edit text validation
     */

    var stateAddNewArticle by mutableStateOf(ArticleFormState())





    fun onEvent(event: ArticleFormEvent) {
        when (event) {

            ArticleFormEvent.SubmitAddArticle -> submitAddArticleData()
            ArticleFormEvent.SubmitAddPatrimoine -> submitAddPatrimoineData()


            is ArticleFormEvent.BarCodeChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(barCode = event.barCode)

            is ArticleFormEvent.DesignationChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(designation = event.designation)

            is ArticleFormEvent.FamilleChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(famille = event.famille)
            is ArticleFormEvent.MarqueChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(marque = event.marque)
            is ArticleFormEvent.PrixHTChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(prixHT = event.prixHT)
            is ArticleFormEvent.PrixTTCChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(prixTTC = event.prixTTC)
            is ArticleFormEvent.QteStationChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(qteStation = event.qteStation)
            is ArticleFormEvent.QteStockChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(qteStock = event.qteStock)
            is ArticleFormEvent.QteTTStationsChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(qteTTStations = event.qteTTStations)
            is ArticleFormEvent.RefChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(ref = event.ref)
            is ArticleFormEvent.TvaChanged -> stateAddNewArticle = stateAddNewArticle.copy(tva = event.tva)
            is ArticleFormEvent.UniteChanged -> stateAddNewArticle = stateAddNewArticle.copy(unite = event.unite)
            is ArticleFormEvent.TypePrixChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(typePrix = event.typePrix)

            is ArticleFormEvent.TypeProduitChanged ->
                stateAddNewArticle = stateAddNewArticle.copy(typeProduit = event.typeProduit)
        }
    }

    private fun submitAddArticleData() {
        val designationResult = validateIsNotEmptyString.execute(stateAddNewArticle.designation)

        val barCodeResult = validateIsNotEmptyString.execute(stateAddNewArticle.barCode)
        val prixUnitaireHTResult = validateDoubleNotZero.execute(StringUtils.stringToDouble(stateAddNewArticle.prixHT))
        val prixTTCResult = validateDoubleNotZero.execute(StringUtils.stringToDouble(stateAddNewArticle.prixTTC))
        val qteStockResult = validateIsNotEmptyString.execute(stateAddNewArticle.qteStock)


        val familletResult = validateIsNotEmptyString.execute(stateAddNewArticle.famille.fAMCode)
        val marqueResult = validateIsNotEmptyString.execute(stateAddNewArticle.marque.mARCode)

        val typePrixResult = validateIsNotEmptyString.execute(stateAddNewArticle.typePrix.type_PrixUnitaireHT)
        val tvaResult = validateIsNotEmptyString.execute(stateAddNewArticle.tva.tVACode)
        val uniteResult = validateIsNotEmptyString.execute(stateAddNewArticle.unite.uNICode)



        val hasError =listOf(
            designationResult,
            barCodeResult,
            prixUnitaireHTResult,
            prixTTCResult,
            qteStockResult,
            familletResult,
            marqueResult,
            typePrixResult,
            tvaResult,
            uniteResult

        ).any { !it.successful }

        if (hasError) {
            stateAddNewArticle = stateAddNewArticle.copy(
                designationError = designationResult.errorMessage,
                barCodeError = barCodeResult.errorMessage,
                prixTTCError = prixTTCResult.errorMessage,
                prixHTError = prixUnitaireHTResult.errorMessage,
                qteStockError = qteStockResult.errorMessage,
                familleError = familletResult.errorMessage,
                marqueError = marqueResult.errorMessage,
                typePrixError =  typePrixResult.errorMessage,
                tvaError =  tvaResult.errorMessage,
                uniteError =  uniteResult.errorMessage,

            )
            return
        }
        viewModelScope.launch {
            validationAddArticleEvents = ValidationArticleEvent.Article(stateAddNewArticle)
        }
    }



    private fun submitAddPatrimoineData() {
        val designationResult = validateIsNotEmptyString.execute(stateAddNewArticle.designation)

        val barCodeResult = validateIsNotEmptyString.execute(stateAddNewArticle.barCode)

        val typeProduitResult = validateIsNotEmptyString.execute(stateAddNewArticle.typeProduit)


        val familletResult = validateIsNotEmptyString.execute(stateAddNewArticle.famille.fAMCode)
        val marqueResult = validateIsNotEmptyString.execute(stateAddNewArticle.marque.mARCode)

        val uniteResult = validateIsNotEmptyString.execute(stateAddNewArticle.unite.uNICode)



        val hasError =listOf(
            designationResult,
            barCodeResult,
            familletResult,
            marqueResult,
            uniteResult,
            typeProduitResult

        ).any { !it.successful }

        if (hasError) {
            stateAddNewArticle = stateAddNewArticle.copy(
                designationError = designationResult.errorMessage,
                barCodeError = barCodeResult.errorMessage,
                familleError = familletResult.errorMessage,
                marqueError = marqueResult.errorMessage,
                uniteError =  uniteResult.errorMessage,
                typeProduitError = typeProduitResult.errorMessage

                )
            return
        }
        viewModelScope.launch {
            validationAddArticleEvents = ValidationArticleEvent.Article(stateAddNewArticle)
        }
    }


    /**
     * Add Famille input edit text validation
     */

    var stateAddNewFamille by mutableStateOf(FamilleFormState())





    fun onAddNewFamilleEvent(event: FamilleFormEvent) {
        when (event) {
            FamilleFormEvent.SubmitAddFamille ->  submitAddFamilleData()
            is FamilleFormEvent.DesignationFamilleChanged ->
                stateAddNewFamille = stateAddNewFamille.copy(designationFamille = event.designationFamille)
            is FamilleFormEvent.OrdreChanged ->
                stateAddNewFamille = stateAddNewFamille.copy(ordre = event.ordre)
            is FamilleFormEvent.StationChanged ->
                stateAddNewFamille = stateAddNewFamille.copy(station = event.station)

            is FamilleFormEvent.DesignationFamilleCourteChanged ->
                stateAddNewFamille = stateAddNewFamille.copy(designationFamilleCourte = event.designationFamilleCourte)
        }
    }
    private fun submitAddFamilleData() {
        val designationFamilleCourteResult = validateIsNotEmptyString.execute(stateAddNewFamille.designationFamilleCourte)
        val designationResult = validateIsNotEmptyString.execute(stateAddNewFamille.designationFamille)


        val ordreResult = validateCanConvertStringToInt.execute(stateAddNewFamille.ordre)

        val stationResult = validateIsNotEmptyString.execute(stateAddNewFamille.station.sTATCode)




        val hasError =listOf(
            designationResult,
            ordreResult,
            stationResult,
            designationFamilleCourteResult

        ).any { !it.successful }

        if (hasError) {
            stateAddNewFamille = stateAddNewFamille.copy(
                designationFamilleError = designationResult.errorMessage,
                designationFamilleCourteError = designationFamilleCourteResult.errorMessage,
                ordreError = ordreResult.errorMessage,
                stationError = stationResult.errorMessage,


                )
            return
        }
        viewModelScope.launch {
            validationAddArticleEvents = ValidationArticleEvent.Famille(stateAddNewFamille)
        }
    }





    /**
     * Add Marque input edit text validation
     */

    var stateAddNewMarque by mutableStateOf(MarqueFormState())





    fun onAddNewMarqueEvent(event: MarqueFormEvent) {
        when (event) {
            MarqueFormEvent.SubmitAddMarque ->  submitAddMarqueData()
            is MarqueFormEvent.DesignationMarqueChanged ->
                stateAddNewMarque = stateAddNewMarque.copy(designationMarque = event.designationMarque)


         }
    }
    private fun submitAddMarqueData() {
        val designationMarqueResult = validateIsNotEmptyString.execute(stateAddNewMarque.designationMarque)







        val hasError =listOf(

            designationMarqueResult

        ).any { !it.successful }

        if (hasError) {
            stateAddNewMarque = stateAddNewMarque.copy(
                designationMarqueError = designationMarqueResult.errorMessage,



                )
            return
        }
        viewModelScope.launch {
            validationAddArticleEvents = ValidationArticleEvent.Marque(stateAddNewMarque)
        }
    }














    fun resetVariable(){
        stateAddNewArticle = stateAddNewArticle.copy(
          designation = "",
         designationError = null,

         barCode = "",
         barCodeError = null,

         ref = "",
         refError = null,

         prixHT = "",
         prixHTError = null,

         prixTTC = "",
         prixTTCError = null,

         qteTTStations = "",
         qteTTStationsError = null,

         qteStation = "",
         qteStationError = null,

         qteStock = "",
         qteStockError = null,

         famille = Famille(),
         familleError = null,

         marque = Marque(),
         marqueError = null,

         typePrix = TypePrixUnitaireHT(),
         typePrixError = null,

         tva = Tva(),
         tvaError = null,

            unite = Unite(),
            uniteError = null,
            )

    }

    fun resetFamilleVariable(){
        stateAddNewFamille = stateAddNewFamille.copy(
            designationFamille = "",
            designationFamilleError = null,
            designationFamilleCourte = "",
            designationFamilleCourteError = null,
            ordre = "",
            ordreError = null,
            station = Station(),
            stationError = null,
        )



    }


    fun resetMarqueVariable(){
        stateAddNewMarque = stateAddNewMarque.copy(
            designationMarque = "",
          designationMarqueError = null
        )



    }
}
