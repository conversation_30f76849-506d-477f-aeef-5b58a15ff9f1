package com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.di

import com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.api.PricePerStationApi
import com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.api.PricePerStationApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object PricePerStationRemoteModule {
    @Provides
    @Singleton
    fun providePricePerStationApi(client: HttpClient): PricePerStationApi = PricePerStationApiImpl(client)

}