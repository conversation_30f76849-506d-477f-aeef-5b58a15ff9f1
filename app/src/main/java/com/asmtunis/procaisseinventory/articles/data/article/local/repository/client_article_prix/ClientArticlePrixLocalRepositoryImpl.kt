package com.asmtunis.procaisseinventory.articles.data.article.local.repository.client_article_prix

import com.asmtunis.procaisseinventory.articles.data.article.domaine.ClientArticlePrix
import com.asmtunis.procaisseinventory.articles.data.article.local.dao.ClientArticlePrixDAO
import kotlinx.coroutines.flow.Flow



class ClientArticlePrixLocalRepositoryImpl(
    private val clientArticlePrixDAO: ClientArticlePrixDAO
) : ClientArticlePrixLocalRepository {
    override fun upsert(value: ClientArticlePrix)  = clientArticlePrixDAO.insert(value)

    override fun upsertAll(value: List<ClientArticlePrix>)  = clientArticlePrixDAO.insertAll(value)

    override fun deleteAll() = clientArticlePrixDAO.deleteAll()

    override fun getAll(): Flow<List<ClientArticlePrix>>  = clientArticlePrixDAO.all


}