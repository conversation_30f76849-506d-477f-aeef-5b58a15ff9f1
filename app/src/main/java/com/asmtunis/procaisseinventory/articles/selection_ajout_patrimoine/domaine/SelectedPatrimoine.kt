package com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine

import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint

data class SelectedPatrimoine(
    //var article: Article = Article(),
    var articleCode: String = "",
    var codeClt: String = "",
    var numSerie: String = "",
    var quantity: Double = 0.0,
    var extraInfo: String = "",
    val imageList: List<ImagePieceJoint> = emptyList(),
    //var marque: Marque = Marque(),
    var marqueCode: String = "",
    var note: String = "",
)
