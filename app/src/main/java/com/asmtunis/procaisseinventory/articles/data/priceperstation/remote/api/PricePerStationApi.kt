package com.asmtunis.procaisseinventory.articles.data.priceperstation.remote.api

import com.asmtunis.procaisseinventory.articles.data.priceperstation.domaine.PricePerStation
import com.asmtunis.procaisseinventory.core.model.DataResult
import kotlinx.coroutines.flow.Flow

interface PricePerStationApi {



    suspend fun getPricesByStation(baseConfig: String): Flow<DataResult<List<PricePerStation>>>
}
