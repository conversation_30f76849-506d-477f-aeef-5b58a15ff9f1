package com.asmtunis.procaisseinventory.articles.data.unite_article.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Entity(tableName = ProCaisseConstants.UNITE_Articles_TABLE, primaryKeys = ["UNITE_ARTICLE_CodeUnite", "UNITE_ARTICLE_CodeArt"])
@Serializable
data class UniteArticle (
    @ColumnInfo(name = "UNITE_ARTICLE_CodeUnite")
    @SerialName("UNITE_ARTICLE_CodeUnite")
    
    var uNITEARTICLECodeUnite: String = "",

    @ColumnInfo(name = "UNITE_ARTICLE_CodeArt")
    @SerialName("UNITE_ARTICLE_CodeArt")
    
    var uNITEARTICLECodeArt: String = "",

    @ColumnInfo(name = "UNITE_ARTICLE_QtePiece")
    @SerialName("UNITE_ARTICLE_QtePiece")
    
    var uNITEARTICLEQtePiece: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_IsUnitaire")
    @SerialName("UNITE_ARTICLE_IsUnitaire")
    
    var uNITEARTICLEIsUnitaire: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_PrixVenteTTC")
    @SerialName("UNITE_ARTICLE_PrixVenteTTC")
    
    var uNITEARTICLEPrixVenteTTC: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_TypePrixVente")
    @SerialName("UNITE_ARTICLE_TypePrixVente")
    
    var uNITEARTICLETypePrixVente: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_user")
    @SerialName("UNITE_ARTICLE_user")
    
    var uNITEARTICLEUser: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_station")
    @SerialName("UNITE_ARTICLE_station")
    
    var uNITEARTICLEStation: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_export")
    @SerialName("UNITE_ARTICLE_export")
    
    var uNITEARTICLEExport: String? = null,

    @ColumnInfo(name = "UNITE_ARTICLE_DDm")
    @SerialName("UNITE_ARTICLE_DDm")
    
    var uNITEARTICLEDDm: String? = null,
)
