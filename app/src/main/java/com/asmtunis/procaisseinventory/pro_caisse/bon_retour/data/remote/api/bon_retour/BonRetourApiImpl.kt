package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.bon_retour

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class BonRetourApiImpl(private val client: HttpClient) : BonRetourApi {
    override suspend fun getBonRetours(baseConfig: String): Flow<DataResult<List<BonRetour>>> = flow {
        val result = executePostApiCall<List<BonRetour>>(
            client = client,
            endpoint = Urls.GET_BON_RETOUR,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchBonRetour(baseConfig: String): Flow<DataResult<List<BonRetour>>> = flow {
        val result = executePostApiCall<List<BonRetour>>(
            client = client,
            endpoint = Urls.ADD_BATCH_BON_RETOUR,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    }