package com.asmtunis.procaisseinventory.pro_caisse.client.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Add
import androidx.compose.material3.DrawerState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SegmentedButton
import androidx.compose.material3.SegmentedButtonDefaults
import androidx.compose.material3.SingleChoiceSegmentedButtonRow
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.adaptive.ExperimentalMaterial3AdaptiveApi
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse.FILTRE_CLIENT
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddClientRoute
import com.asmtunis.procaisseinventory.core.navigation.ClientsInfoRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.floating_button.SnapScrollingButton
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.PullToRefreshLazyColumn
import com.asmtunis.procaisseinventory.shared_ui_components.lazy_column.RowClientList
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterContainer
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.FilterSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.SearchSectionComposable
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.ListEvent
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.simapps.ui_kit.ModifiersUtils.floatingBtnIsVisible
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3AdaptiveApi::class)
@Composable
fun ClientListPane(
    navigate: (route: Any) -> Unit,
    navigatorTo: () -> Unit = {},
    drawer: DrawerState,
    settingViewModel: SettingViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    mainViewModel: MainViewModel,
    dataViewModel : DataViewModel,
    locationViewModule : LocationViewModule,
    networkViewModel: NetworkViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {

    val authorizationList = getProCaisseDataViewModel.authorizationList

    val canFilterClient = authorizationList.any { it.AutoCodeAu == FILTRE_CLIENT }

    val uiWindowState = settingViewModel.uiWindowState

    val  clientViewModel = proCaisseViewModels.clientViewModel

   val selectedClient = clientViewModel.selectedClient

    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val snackbarHostState = remember { SnackbarHostState() }
    val listState = rememberLazyListState()

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val isConnected = networkViewModel.isConnected
    val showSearchView = clientViewModel.showSearchView
    val searchTextState = clientViewModel.searchTextState

    val utilisateur = mainViewModel.utilisateur
    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: ""
    val state = clientViewModel.clientsListstate
    val listOrder = state.listOrder
    val filterList = context.resources.getStringArray(R.array.clients_filter)
    val listFilter = state.filter
    val filterByType = state.filterByType
    val filterByClientEtat = state.filterByClientEtat

    LaunchedEffect(key1 = searchTextState.text, key2 = state.lists, key3 = state.filter) {
        clientViewModel.filterClients(state)
    }
    LaunchedEffect(key1 = clientViewModel.filterBySold) {
        clientViewModel.filterClients(state)
    }

    LaunchedEffect(key1 = Unit) {
        // reset selected client (when navigate to bl bc br .. from nav drawer

        scope.launch {
            delay(1000)
            mainViewModel.resetClientByCode()
        }

    }


    val isVisible = floatingBtnIsVisible(
        listeSize = state.lists.size,
        canScrollForward = listState.canScrollForward
    )


    val canAddClients = authorizationList.any { it.AutoCodeAu == AuthorizationValuesProCaisse.AJOUT_CLIENT }

     Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) },
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = {
                    scope.launch { drawer.open() }
                },
                showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                title = stringResource(id = navigationDrawerViewModel.proCaisseSelectedMenu.title),
                titleVisibilty = !showSearchView,

                //  showNavIcom = searchTextState.text.isBlank(),

                actions = {
                    if(canFilterClient) {
                        SearchSectionComposable(
                            label = context.getString(
                                R.string.filter_by,
                                when (listFilter) {
                                    is ListSearch.FirstSearch -> filterList[0]
                                    is ListSearch.SecondSearch -> filterList[1]
                                    else -> filterList[2]}),
                            searchVisibility  = showSearchView,
                            searchTextState = searchTextState,
                            onSearchValueChange = { clientViewModel.onSearchValueChange(TextFieldValue(it)) },
                            onShowSearchViewChange = { clientViewModel.onShowSearchViewChange(it) },
                            onShowCustomFilterChange = { clientViewModel.onShowCustomFilterChange(it) }
                        )
                    }

                }
            )
        },
        //    containerColor = colorResource(id = R.color.black),


        floatingActionButton = {
            val density = LocalDensity.current
            Column(
                verticalArrangement = Arrangement.spacedBy(12.dp) ,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AnimatedVisibility(
                    // modifier = modifier,
                    visible = isVisible && canAddClients,
                    enter = slideInVertically { with(density) { 40.dp.roundToPx() } } + fadeIn(), exit = fadeOut(animationSpec = keyframes { this.durationMillis = 120 })
                ) {
                    ExtendedFloatingActionButton(

                        shape = if(!listState.isScrollInProgress) FloatingActionButtonDefaults.extendedFabShape else FloatingActionButtonDefaults.shape ,
                        onClick = {


                           // mainViewModel.onSelectedClientChange(Client())

                            val prefixe = mainViewModel.prefixList.firstOrNull { it.pREIdTable == "Client" }?.pREPrefixe?: "InvD_"

                            mainViewModel.generateCodeM(
                                utilisateur = utilisateur,
                                prefix = prefixe
                            )
                            navigate(AddClientRoute())
                            /*
                              To delete clt with undo snackbar
                              mainViewModel.onEvent(ListEvent.DeleteNote(state.lists[0] as Client))
                              scope.launch {
                                  val result = snackbarHostState.showSnackbar(
                                      message = "Note deleted",
                                      actionLabel = "Undo"
                                  )
                                  if (result == SnackbarResult.ActionPerformed) {
                                      mainViewModel.onEvent(ListEvent.RestoreNote)
                                  }
                              }*/
                        }
                    ) {
                        Icon(
                            imageVector = Icons.TwoTone.Add,
                            contentDescription = stringResource(id = R.string.add_Client_button)
                        )
                    }
                }




                SnapScrollingButton(
                    isScrollInProgress = listState.isScrollInProgress,
                    isVisible = remember { derivedStateOf { listState.firstVisibleItemIndex } }.value > 15 && isVisible,
                    density = density,
                    animateScrollToItem = { listState.animateScrollToItem(index = it) }
                )
            }







        }
    ) { padding ->
         if (clientViewModel.showCustomFilter) {
             FilterContainer(
                 filterList = filterList,
                 listFilter = listFilter,
                 listOrder = listOrder,
                 orderList = context.resources.getStringArray(R.array.clients_order),
                 onShowCustomFilterChange = { clientViewModel.onShowCustomFilterChange(false) },
                 onEvent = { clientViewModel.onEvent(event = it) },
                 customFilterContent = {
                     val typeClientList = context.resources.getStringArray(R.array.clients_type_list)

                     FilterSectionComposable(
                         title = stringResource(id = R.string.filter_by_type_clt),
                         currentFilterLable = filterByType,
                         onAllEvent = {
                             clientViewModel.onEvent(ListEvent.FirstCustomFilter(""))
                         },
                         onEvent = { clientViewModel.onEvent(ListEvent.FirstCustomFilter(typeClientList[it])) },

                         filterCount = typeClientList.size,
                         customFilterCode = {
                             typeClientList[it]
                         },
                         filterLabel = { typeClientList[it] }
                     )


                     val etatClientList = context.resources.getStringArray(R.array.client_etat)

                     FilterSectionComposable(
                         title = stringResource(id = R.string.filter_by_etat_clt),
                         currentFilterLable = filterByClientEtat,
                         onAllEvent = { clientViewModel.onEvent(ListEvent.SecondCustomFilter("")) },
                         onEvent = { clientViewModel.onEvent(ListEvent.SecondCustomFilter(etatClientList[it])) },
                         filterCount = etatClientList.size,
                         customFilterCode = { etatClientList[it] },
                         filterLabel = { etatClientList[it] }
                     )
                 }
             )


         }


        Column(
            verticalArrangement = Arrangement.Top,
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                //  .background(colorResource(id = R.color.black))
                .fillMaxSize()
                .padding(padding)
                .padding(top = 12.dp)
        ) {

                SingleChoiceSegmentedButtonRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 12.dp, end = 12.dp)
                ) {

                    SegmentedButton(
                        shape = SegmentedButtonDefaults.itemShape(
                            index = 0,
                            count = 3
                        ),
                        onClick = { clientViewModel.onfilterBySoldChange(2) },
                        selected = clientViewModel.filterBySold == 2
                    ) {
                        Text(
                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                            text = stringResource(id = R.string.all_label_f),
                            style = MaterialTheme.typography.bodySmall,
                            softWrap = false,
                            maxLines = 1
                        )
                    }

                    SegmentedButton(
                        shape = SegmentedButtonDefaults.itemShape(
                            index = 1,
                            count = 3
                        ),
                        onClick = { clientViewModel.onfilterBySoldChange(1) },
                        selected = clientViewModel.filterBySold == 1
                    ) {
                        Text(
                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                            text = stringResource(id = R.string.sold_positif),
                            style = MaterialTheme.typography.bodySmall,
                            softWrap = false,
                            maxLines = 1
                        )
                    }



                    SegmentedButton(
                        shape = SegmentedButtonDefaults.itemShape(index = 2, count = 3),
                        onClick = { clientViewModel.onfilterBySoldChange(0) },
                        selected = clientViewModel.filterBySold == 0
                    ) {
                        Text(
                            modifier = Modifier.horizontalScroll(rememberScrollState()),
                            text = stringResource(id = R.string.sold_negatif),
                            style = MaterialTheme.typography.bodySmall,
                            softWrap = false,
                            maxLines = 1
                        )
                    }


                }


            if (getProCaisseDataViewModel.clientsState.loading) {
                LottieAnim(lotti = R.raw.loading, size = 250.dp)
            } else {
                if (clientViewModel.clientsListstate.lists.isNotEmpty()) {
                    CustomersList(
                        selectedClient = selectedClient,
                        isConnected = isConnected,
                        utilisateur = utilisateur,
                        exerciceCode = exerciceCode,
                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                        selectedBaseconfig = selectedBaseconfig,
                        listState = listState,
                        filteredClient = clientViewModel.clientsListstate.lists,
                        onItemClick = {

                            locationViewModule.resetCurrentLocation()
                            clientViewModel.onSelectedClientChange(it)
                            navigate(ClientsInfoRoute(clientId = it.cLICode))


                        }
                    )
                } else {
                    LottieAnim(lotti = R.raw.emptystate, size = 250.dp)
                }
            }

        }
   }
}



@Composable
fun CustomersList (
    isConnected: Boolean,
    selectedClient: Client,
    utilisateur: Utilisateur,
    exerciceCode: String,
    selectedBaseconfig: BaseConfig,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    listState: LazyListState,
    filteredClient: List<Client>,
    onItemClick: (Client) -> Unit


) {
    // val state = remember { clientViewModel.state.value }

    val isRefreshing  = getProCaisseDataViewModel.clientsState.loading

    PullToRefreshLazyColumn(
        items = filteredClient,
        lazyListState = listState,
        isRefreshing = isRefreshing,
        pullToRefreshEnabled = !filteredClient.any { !it.isSync } && isConnected,
        onRefresh = {
            getProCaisseDataViewModel.getClient(
                fromUpdate = true,
                baseConfig = selectedBaseconfig,
                utilisateur = utilisateur,
                exerciceCode = exerciceCode
            )

        },
        key = { client -> client.id },
        content = { client ->
            RowClientList(
                selectedClient = selectedClient,
                client = client,
                onClick = { onItemClick(client) }
            )
        },
    )

}