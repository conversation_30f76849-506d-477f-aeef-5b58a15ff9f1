package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.remote.di

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.remote.api.OrdreMissionApi
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.remote.api.OrdreMissionApiImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import io.ktor.client.HttpClient
import javax.inject.Singleton


    @Module
    @InstallIn(SingletonComponent::class)
    object OrdreMissionRemoteModule {
        @Provides
        @Singleton
        fun provideOrdreMissionApi(client: HttpClient): OrdreMissionApi = OrdreMissionApiImpl(client)

    }