package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.dao.InventaireDAO
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.repository.InventaireLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.repository.InventaireLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton



    @Module
    @InstallIn(SingletonComponent::class)
    class InventaireLocalModule {

        @Provides
        @Singleton
        fun provideInventairePatrimoineDao(
            proCaisseDataBase: ProCaisseDataBase
        ) = proCaisseDataBase.inventairePatrimoineDAO()

        @Provides
        @Singleton
        @Named("InventairePatrimoine")
        fun provideInventairePatrimoineRepository(
            inventaireDAO: InventaireDAO
        ): InventaireLocalRepository = InventaireLocalRepositoryImpl(
            inventaireDAO = inventaireDAO
        )


    }