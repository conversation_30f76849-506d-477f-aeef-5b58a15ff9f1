package com.asmtunis.procaisseinventory.pro_caisse.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.material3.FilledTonalButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

@Composable
fun RadioButtonChoiceView(
    title : String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit = {},
    selectedOption: String,
    onOptionSelected: (String) -> Unit = {},
    priceCategoryList: List<String>
) {


    //val (selectedOption, onOptionSelected) = remember { mutableStateOf(radioOptions[0]) }
// Note that Modifier.selectableGroup() is essential to ensure correct accessibility behavior

    Column (
        modifier = Modifier.padding(16.dp),
        verticalArrangement = Arrangement.Top,
    horizontalAlignment = Alignment.Start,
    ){
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = title,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold,
        )
        Spacer(modifier = Modifier.height(12.dp))
        Column(Modifier.selectableGroup()) {
            priceCategoryList.forEach { text ->
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(56.dp)
                        .selectable(
                            selected = (text == selectedOption),
                            onClick = {
                                onOptionSelected(text)
                                onDismiss()
                            },
                            role = Role.RadioButton
                        )
                        .padding(horizontal = 16.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    RadioButton(
                        selected = (text == selectedOption),
                        onClick = null // null recommended for accessibility with screenreaders
                    )
                    Text(
                        text = text,
                        style = MaterialTheme.typography.bodyLarge,
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))


        Row(
            modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.End,
        verticalAlignment = Alignment.CenterVertically,
        ) {


          /*  FilledTonalButton(
                onClick = {
                    onConfirm()
                },
                shape = MaterialTheme.shapes.medium
            ) {
                Text(
                    text = "Oui",
                    textAlign = TextAlign.End
                )
            }
            Spacer(modifier = Modifier.width(12.dp))*/

            FilledTonalButton(
                onClick = {
                    onDismiss()
                },
                shape = MaterialTheme.shapes.medium
            ) {
                Text(
                    text = "Exit",
                    textAlign = TextAlign.End
                )
            }


        }





    }

}