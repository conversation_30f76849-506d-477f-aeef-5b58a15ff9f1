package com.asmtunis.procaisseinventory.pro_caisse.ui

import android.content.Context
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.unite_article.domaine.UniteArticle
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.domaine.SelectedArticle
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.core.authorizations.AuthorizationFunction.haveDiscountAuth
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertDoubleToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToDoubleFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.convertStringToPriceFormat
import com.asmtunis.procaisseinventory.core.utils.StringUtils.removeTrailingZeroInDouble
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.data.tva.domaine.Tva
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField

@Composable
fun SetArticleView(
    toaster: ToasterState,
    hasPromo: Boolean,
    isProInventory: Boolean,
    proCaisseAuthorization: List<Authorization>,
    selectedArticle: SelectedArticle,
    onSelectedPriceCategorieChange: (String) -> Unit,
    prixUnitaire: String,
    priceCategoryList: List<String>,
    onQuantityChange: (String) -> Unit,
    onRemiseChange: (String) -> Unit,
    onPrixCaisseChange: (String) -> Unit,
    onPrixTotalChange: (String) -> Unit,
    onPrixHTChange: (String) -> Unit,
    onPrixVenteChange: (String) -> Unit,
    showPriceCategorix: (Boolean) -> Unit,
    priceCategoryVisibility: Boolean,
    showTva: Boolean,
    tvaList: List<Tva>,
    selectedTva: Tva,
    onSelectedTvaChange: (Tva) -> Unit,
    onTvaExpandedChange: (Boolean) -> Unit,
    tvaExpanded: Boolean,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit,
    context: Context,
    unitArticleList: List<UniteArticle>,
    showUnitArticle: Boolean,
    unitArticleExpanded: Boolean,
    onSelectedUnitArticleChange: (UniteArticle) -> Unit,
    onUnitArticleExpandedChange: (Boolean) -> Unit,
) {

    val scrollState = rememberScrollState()

    val article = selectedArticle.article

    val selectedUnitArticle = if(selectedArticle.uniteArticle != UniteArticle()) selectedArticle.uniteArticle else  unitArticleList.firstOrNull { it.uNITEARTICLECodeUnite == article.uNITEARTICLECodeUnite}?: UniteArticle(uNITEARTICLECodeUnite = article.uNITEARTICLECodeUnite?: "")


    val haveModifyPriceAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.MODIFY_PRICE }

    val haveFixedDiscountAuthorisation = proCaisseAuthorization.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT }
    val haveFreeDiscountAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.FREE_DISCOUNT }
    val haveNoDiscountAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.NO_DISCOUNT }
    val haveArtRemiseDiscountAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.ART_REM_DISCOUNT }
    val haveChoosePriceCategoriAuthorisation = !proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.CHOOSE_PRICE_CATEGERI }


    val selectedArticleQty = selectedArticle.quantity

    val artQty = stringToDouble(article.aRTQteStock)
    Column(
        modifier = Modifier
            .verticalScroll(scrollState)
            .fillMaxWidth()
            .padding(start = 12.dp, end = 12.dp),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.height(12.dp))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = article.aRTDesignation,
            textAlign = TextAlign.Center,
            fontWeight = FontWeight.Bold
        )

        Spacer(modifier = Modifier.height(9.dp))
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = article.aRTCodeBar,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))
        if (!showTva) {
            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(),
                designation = convertStringToPriceFormat(prixUnitaire),
                errorValue = null,
                itemExpanded = priceCategoryVisibility,
                selectedItem = selectedArticle.selectedPriceCategory,
                label = stringResource(R.string.unit_price_field_title) + if (haveChoosePriceCategoriAuthorisation) "" else stringResource(R.string.fixe),
                readOnly = true,
                showTrailingIcon = haveChoosePriceCategoriAuthorisation,
                itemList = priceCategoryList,
                getItemDesignation = { it },
                onClick = {
                    onSelectedPriceCategorieChange(it)
                    showPriceCategorix(false)
                },
                onItemExpandedChange = {
                    if (haveChoosePriceCategoriAuthorisation) showPriceCategorix(it)
                    else

                        showToast(
                            context = context,
                            toaster = toaster,
                            message = context.getString(R.string.info) + " \n"+ context.getString(R.string.pas_autorisation_changer_cat_prix),
                            type =  ToastType.Info,
                        )
                },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }
            )
        }


        EditTextField(
            modifier = Modifier.fillMaxWidth(),
            text = selectedArticleQty,
            errorValue = selectedArticle.quantityError?.asString(),
            label = stringResource(
                R.string.qty_en_stock,
                "${removeTrailingZeroInDouble(convertDoubleToDoubleFormat(artQty))} ${article.uNITEARTICLECodeUnite}"
            ),
            onValueChange = {
                onQuantityChange(it)
            },
            readOnly = false,
            enabled = true,
            showTrailingIcon = true,
            keyboardType = KeyboardType.Decimal,
            imeAction = ImeAction.Next
        )


        if (showTva) {
            EditTextField(
                modifier = Modifier.fillMaxWidth(),
                text = selectedArticle.lTMtBrutHT,
                errorValue = null,
                label = stringResource(R.string.total_achat_ht),
                onValueChange = {
                    onPrixHTChange(it)
                },
                readOnly = false,
                enabled = !hasPromo,
                showTrailingIcon = true,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )
        }

       // if(isProInventory) {
            EditTextField(
                modifier = Modifier.fillMaxWidth(),
                text = convertStringToDoubleFormat(input = selectedArticle.lTMtTTC),
              //  text = selectedArticle.lTMtTTC,
                errorValue = selectedArticle.mtTTCError?.asString(),
                label = if (showTva) stringResource(R.string.price_achat_dt) else stringResource(R.string.total_price_ttc_field_title),
                onValueChange = {
                    onPrixTotalChange(it)
                },
                readOnly = !haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization),
                enabled = true,
                showTrailingIcon = haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization),
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )
      //  }

        if (showTva) {
            EditTextField(
                modifier = Modifier.fillMaxWidth(),
                text = selectedArticle.prixVente,
                errorValue = null,
                label = stringResource(R.string.price_vente_dt),
                onValueChange = {
                    onPrixVenteChange(it)
                },
                readOnly = false,
                enabled = !hasPromo,
                showTrailingIcon = true,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )
        }

        if (haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization) || isProInventory) {
            EditTextField(
                modifier = Modifier.fillMaxWidth(),
                text = selectedArticle.discount,
                errorValue =
                // if have this auth  getModifyPriceAuthorization user can update price
                if (haveArtRemiseDiscountAuthorisation) {
                    if (article.artMaxTRemise != null) {
                        if (stringToDouble(selectedArticle.discount) > stringToDouble(article.artMaxTRemise)) {
                          stringResource(R.string.max_value_title, removeTrailingZeroInDouble(article.artMaxTRemise!!))


                        } else {
                            if (stringToDouble(selectedArticle.discount) > 100 || stringToDouble(
                                    selectedArticle.discount
                                ) < 0
                            ) {

                                stringResource(R.string.discount_field_error)

                            } else {
                                null
                            }
                        }
                    } else null
                }
                else if (haveFreeDiscountAuthorisation) {
                    if (stringToDouble(selectedArticle.discount) > 100 || stringToDouble(
                            selectedArticle.discount
                        ) < 0
                    ) {

                        stringResource(R.string.discount_field_error, "100")
                    } else {
                        null
                    }
                }
                else {
                    if (haveFixedDiscountAuthorisation?.AutValues != null) {
                        if (stringToDouble(selectedArticle.discount) > stringToDouble(haveFixedDiscountAuthorisation.AutValues))
                            stringResource(R.string.max_discount, haveFixedDiscountAuthorisation.AutValues)
                        else null
                    } else null

                },
                label = stringResource(R.string.discount_perc),
                onValueChange = {
                    if (stringToDouble(selectedArticleQty) <= 0.0) {
                        if (artQty >= 1)
                            onQuantityChange("1")
                        else if (0 < artQty) onQuantityChange("0.1")
                        else onQuantityChange("0.0")

                    }
                    onRemiseChange(if (stringToDouble(it) > 100.0) "100" else if (stringToDouble(it) < 0.0) "0" else it)
                },
                readOnly = false,
                enabled = !hasPromo,
                showTrailingIcon = true,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )
        }



        if (showTva) {
            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(),
                designation = removeTrailingZeroInDouble(convertStringToDoubleFormat(selectedTva.tVACode)).ifEmpty { "0" },
                errorValue = null,
                label = stringResource(R.string.tva_perc),
                readOnly = true,
                itemList = tvaList,
                selectedItem = selectedTva,
                itemExpanded = tvaExpanded,
                getItemDesignation = { removeTrailingZeroInDouble(convertStringToDoubleFormat(it.tVACode)).ifEmpty { "0" } },
                onClick = {
                    onSelectedTvaChange(it)
                    onTvaExpandedChange(false)


                },
                onItemExpandedChange = {
                    onTvaExpandedChange(it)
                },
                getItemTrailing = { "%" },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }

            )
        }


        if(showUnitArticle) {
            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(),
                designation = selectedUnitArticle.uNITEARTICLECodeUnite,
                errorValue = null,
                label = stringResource(R.string.unite_article_field_title),
                readOnly = true,
                itemList = unitArticleList,
                selectedItem = selectedUnitArticle,
                itemExpanded = unitArticleExpanded,
                getItemDesignation = { it.uNITEARTICLECodeUnite },
                onClick = {
                    onSelectedUnitArticleChange(it)
                    onUnitArticleExpandedChange(false)
                },
                onItemExpandedChange = {
                    onUnitArticleExpandedChange(it)
                },
                getItemTrailing = { "" },
                lottieAnimEmpty = {
                    LottieAnim(lotti = R.raw.emptystate)
                },
                lottieAnimError = {
                    LottieAnim(lotti = R.raw.connection_error, size = it)
                }

            )
        }

        if (((haveDiscountAuth(proCaisseAuthorization = proCaisseAuthorization) && !showTva) || haveModifyPriceAuthorisation) && !isProInventory) {
            EditTextField(
                modifier = Modifier.fillMaxWidth(),
                text = convertStringToDoubleFormat(input = selectedArticle.prixCaisse),
                errorValue = selectedArticle.discountError?.asString(),
                label = stringResource(R.string.chached_price_field_title_dt),
                onValueChange = {
                    onPrixCaisseChange(it)
                },
                readOnly = false,
                enabled = true,
                showTrailingIcon = true,
                keyboardType = KeyboardType.Decimal,
                imeAction = ImeAction.Next
            )
        }
        //   Spacer(modifier = Modifier.height(12.dp))


    /*    Text(text = "prixVente " + selectedArticle.prixVente)
        Text(text = "lTMtTTC " + selectedArticle.lTMtTTC)
        Text(text = "lTMtNetHT " + selectedArticle.lTMtNetHT)
        Text(text = "lTPuTTC " + selectedArticle.lTPuTTC)
        Text(text = "lTPuHT " + convertStringToDoubleFormat(selectedArticle.lTPuHT))
        Text(text = "lTMtHT " + selectedArticle.lTMtBrutHT)
        Text(text = "mntTva " + selectedArticle.mntTva)
        Text(text = "mntDiscount " + selectedArticle.mntDiscount)
        Text(text = "discount " + selectedArticle.discount)
*/
        Spacer(modifier = Modifier.height(12.dp))

        Row(
            horizontalArrangement = Arrangement.SpaceAround,
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            OutlinedButton(
                enabled = if (haveFixedDiscountAuthorisation != null /*&& !haveModifyPriceAuthorisation*/)
                    stringToDouble(selectedArticle.discount) <= stringToDouble(
                        haveFixedDiscountAuthorisation.AutValues
                    )
                else stringToDouble(selectedArticleQty) > 0.0,
                onClick = {
                    onConfirm()
                },

                shape = MaterialTheme.shapes.medium
            ) {
                Text(text = stringResource(R.string.submit), textAlign = TextAlign.Center)
            }


            TextButton(
                onClick = {
                    onDismiss()
                }
            ) {
                Text(
                    //modifier = Modifier.customWidth(LocalConfiguration.current, 0.5f),
                    text = stringResource(R.string.cancel), textAlign = TextAlign.Center,
                    color = MaterialTheme.colorScheme.error
                )
            }
        }
        Spacer(modifier = Modifier.height(20.dp))

    }
}