package com.asmtunis.procaisseinventory.pro_caisse.client.text_validation

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateDoubleNotZero
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateEmail
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateList
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePassword
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidatePhoneNumber
import com.asmtunis.procaisseinventory.core.textvalidation.use_cases.ValidateStringNotEmpty
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.coroutines.launch
import java.util.Locale


class ClientTextValidationViewModel(
    private val validateIsNotEmptyString: ValidateStringNotEmpty = ValidateStringNotEmpty(),
    private val validateList: ValidateList = ValidateList(),
    private val validateEmail: ValidateEmail = ValidateEmail(),
    private val validatePassword: ValidatePassword = ValidatePassword(),
    private val validatePhoneNumber: ValidatePhoneNumber = ValidatePhoneNumber(),
    private val validateDoubleNotZero: ValidateDoubleNotZero = ValidateDoubleNotZero()
) : ViewModel() {







    /**
     * Add Client input edit text validation
     */

    var stateAddClient by mutableStateOf(AddClientFormState())
        private set

   // private val validationAddClientEventChannel = Channel<ValidationAddClientEvent>()
   // val validationAddClientEvents = validationAddClientEventChannel.receiveAsFlow()

    var validationAddClientEvents: ValidationAddClientEvent by mutableStateOf(ValidationAddClientEvent())


    fun onvalidationAddClientEventsChange (value: ValidationAddClientEvent) {
        validationAddClientEvents = value
    }
    fun onAddClientEvent(event: AddClientFormEvent) {
        when (event) {
            is AddClientFormEvent.AdresseChanged -> {
                val addresseResult = validateIsNotEmptyString.execute(event.adresse)

                stateAddClient = stateAddClient.copy(
                    addresse = event.adresse,
                    addresseError = addresseResult.errorMessage
                )
            }

            is AddClientFormEvent.DelegationChanged ->
                stateAddClient = stateAddClient.copy(delegation = event.delegation)

            is AddClientFormEvent.EmailChanged -> {
                val emailResult = if(event.email.isNotEmpty()) validateEmail.execute(event.email) else validateEmail.execute("<EMAIL>") // only used to return  ValidationResult

                stateAddClient = stateAddClient.copy(email = event.email, emailError = emailResult.errorMessage)
            }

            is AddClientFormEvent.GouvernoratChanged ->
                stateAddClient = stateAddClient.copy(gouvernorat = event.gouvernorat)

            is AddClientFormEvent.LatitudeChanged ->
                stateAddClient = stateAddClient.copy(latitude = event.latitude)

            is AddClientFormEvent.LongitudeChanged ->
                stateAddClient = stateAddClient.copy(longitude = event.longitude)

            is AddClientFormEvent.MatriculeFiscaleChanged -> {
                val matriculeFiscalResult = validateIsNotEmptyString.execute(event.matriculeFiscale)

                stateAddClient = stateAddClient.copy(
                    matriculeFiscal = event.matriculeFiscale,
                    matriculeFiscalError = matriculeFiscalResult.errorMessage
                )
            }

            is AddClientFormEvent.NomChanged -> {
                val nomResult = validateIsNotEmptyString.execute(event.nom)

                stateAddClient = stateAddClient.copy(
                    nom = event.nom,
                    nomError = nomResult.errorMessage
                )
            }

            is AddClientFormEvent.NomsocieteChanged ->
                stateAddClient = stateAddClient.copy(nomSociete = event.nomsociete)

            is AddClientFormEvent.PhoneNumber1Changed ->
                stateAddClient = stateAddClient.copy(phone1 = event.phonenumber1)

            is AddClientFormEvent.PhoneNumber2Changed ->
                stateAddClient = stateAddClient.copy(phone2 = event.phonenumber2)

            is AddClientFormEvent.ProffessionChanged ->
                stateAddClient = stateAddClient.copy(proffession = event.proffession)

            AddClientFormEvent.SubmitAddClient ->
                submitAddClientData()

            is AddClientFormEvent.CountryData1Changed ->
                stateAddClient = stateAddClient.copy(countryData1 = event.countryData1)

            is AddClientFormEvent.CountryData2Changed ->
                stateAddClient = stateAddClient.copy(countryData2 = event.countryData2)

            is AddClientFormEvent.HaveCreditChanged ->
                stateAddClient = stateAddClient.copy(haveCredit = event.haveCredit)

            is AddClientFormEvent.HaveTimbreChanged ->
                stateAddClient = stateAddClient.copy(haveTimbre = event.haveTimbre)

            is AddClientFormEvent.TypeClientChanged ->
                stateAddClient = stateAddClient.copy(typeClient = event.typeClient)

        }
    }

    private fun submitAddClientData() {
        val nomResult = validateIsNotEmptyString.execute(stateAddClient.nom)

        val addresseResult = validateIsNotEmptyString.execute(stateAddClient.addresse)
        val matriculeFiscalResult = validateIsNotEmptyString.execute(stateAddClient.matriculeFiscal)

        val emailResult = if(stateAddClient.email.isNotEmpty()) validateEmail.execute(stateAddClient.email) else validateEmail.execute("<EMAIL>") // only used to return  ValidationResult

        val hasError = listOf(
            nomResult,
            addresseResult,
            matriculeFiscalResult,
            emailResult
        ).any { !it.successful }

        if (hasError) {
            stateAddClient = stateAddClient.copy(
                nomError = nomResult.errorMessage,
                addresseError = addresseResult.errorMessage,
                matriculeFiscalError = matriculeFiscalResult.errorMessage

            )
            return
        }
        viewModelScope.launch {
            validationAddClientEvents = ValidationAddClientEvent.AddClient(stateAddClient)
        }
    }






    fun resetClientVariable(){
        stateAddClient = AddClientFormState()
    }
    
    
    
    
    fun setClientVariable(client: Client, typeClientList: List<String>, utilisateur: Utilisateur) {
        resetClientVariable()

        onAddClientEvent(AddClientFormEvent.HaveTimbreChanged(client.cLITimbre == "1"))
        onAddClientEvent(AddClientFormEvent.HaveCreditChanged(client.cLIIsCredit == "1"))



        if (utilisateur.typeUser.lowercase(Locale.getDefault()) == Globals.PROSPECT.lowercase(Locale.getDefault()))
            onAddClientEvent(AddClientFormEvent.TypeClientChanged(typeClientList.first { it == Globals.PROSPECT }))

        else onAddClientEvent(AddClientFormEvent.TypeClientChanged(client.cLIType))





        onAddClientEvent(AddClientFormEvent.NomChanged(client.cLINomPren))

        client.cLIMatFisc?.let { AddClientFormEvent.MatriculeFiscaleChanged(it) }
            ?.let { onAddClientEvent(it) }


        client.cLITel1?.let { AddClientFormEvent.PhoneNumber1Changed(it) }?.let { onAddClientEvent(it) }

        client.cLITel2?.let { AddClientFormEvent.PhoneNumber2Changed(it ) }?.let { onAddClientEvent(it) }
        client.cltGouvernorat?.let { AddClientFormEvent.GouvernoratChanged(it ) }?.let { onAddClientEvent(it) }
        client.cltVille?.let { AddClientFormEvent.DelegationChanged(it ) }?.let { onAddClientEvent(it) }
        client.cLIAdresse?.let { AddClientFormEvent.AdresseChanged( it ) }?.let { onAddClientEvent(it) }
        client.cLIMail?.let { AddClientFormEvent.EmailChanged(it) }?.let { onAddClientEvent(it) }
        client.cLtProfession?.let { AddClientFormEvent.ProffessionChanged(it) }?.let { onAddClientEvent(it) }
        client.cLtNomMagasin?.let { AddClientFormEvent.NomsocieteChanged(it) }?.let { onAddClientEvent(it) }
        client.cltLongitude?.let { AddClientFormEvent.LongitudeChanged(it.toString()) }?.let { onAddClientEvent(it) }
        client.cltLatitude?.let { AddClientFormEvent.LatitudeChanged(it.toString()) }?.let { onAddClientEvent(it) }


    }

}
