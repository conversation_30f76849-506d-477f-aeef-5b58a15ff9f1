package com.asmtunis.procaisseinventory.pro_caisse.client.screens

import NavDrawer
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.adaptive.ExperimentalMaterial3AdaptiveApi
import androidx.compose.material3.adaptive.layout.AnimatedPane
import androidx.compose.material3.adaptive.layout.ListDetailPaneScaffoldRole
import androidx.compose.material3.adaptive.navigation.NavigableListDetailPaneScaffold
import androidx.compose.material3.adaptive.navigation.rememberListDetailPaneScaffoldNavigator
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import com.asmtunis.procaisseinventory.articles.selection_ajout_article_calcul.SelectArticleCalculViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.AddClientRoute
import com.asmtunis.procaisseinventory.core.navigation.ClientsInfoRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterial3AdaptiveApi::class)
@Composable
fun ClientScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    mainViewModel: MainViewModel,
    dataViewModel : DataViewModel,
    locationViewModule : LocationViewModule,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    settingViewModel: SettingViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    syncInventoryViewModel: SyncInventoryViewModel,
    selectArtMobilityVM : SelectArticleCalculViewModel,
    proCaisseViewModels: ProCaisseViewModels
    ) {
    val clientViewModel = proCaisseViewModels.clientViewModel

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

    val scope = rememberCoroutineScope()
    val uiWindowState = settingViewModel.uiWindowState

    val navigator = rememberListDetailPaneScaffoldNavigator<Any>()

  NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navigationDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncInventoryViewModel = syncInventoryViewModel,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        NavigableListDetailPaneScaffold(
           // modifier = Modifier.fillMaxSize().padding(padding),
            navigator = navigator,
            listPane = {
                ClientListPane(
                    navigate = {
                        when (it) {
                            is AddClientRoute -> {
                                navigate(AddClientRoute())
                            }
                            is ClientsInfoRoute -> {
                                scope.launch {
                                    navigator.navigateTo(
                                        pane = ListDetailPaneScaffoldRole.Detail,
                                        contentKey = it.clientId
                                    )
                                }
                            }
                            else -> {
                                // Handle other cases or raise an error if needed
                                println("Unknown argument type")
                            }
                        }
                      },
                    navigatorTo = {
                        scope.launch {
                            navigator.navigateTo(
                                pane = ListDetailPaneScaffoldRole.Detail
                            )
                        }
                    },
                    drawer = drawer,
                    navigationDrawerViewModel = navigationDrawerViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    mainViewModel = mainViewModel,
                    dataViewModel = dataViewModel,
                    locationViewModule = locationViewModule,
                    networkViewModel = networkViewModel,
                    proCaisseViewModels = proCaisseViewModels,
                    settingViewModel = settingViewModel
                    )

            },
            detailPane = {

           val selectedClientCode = navigator.currentDestination?.contentKey?.toString()
         // val content = navigator.currentDestination?.content?.toString()?: mainViewModel.clientList.firstOrNull()?.cLICode?: ""


            if(uiWindowState.navigationType != ReplyNavigationType.PERMANENT_NAVIGATION_DRAWER) {
                    clientViewModel.onSelectedClientChange(Client())
                }
            else
                if(selectedClientCode == null) {
                    clientViewModel.onSelectedClientChange(mainViewModel.clientList.firstOrNull()?: Client())
                }
                AnimatedPane {
                    ClientInfoScreen(
                        navigate = { navigate(it) },
                        popBackStack = { scope.launch { navigator.navigateBack() } },
                        mainViewModel = mainViewModel,
                        dataViewModel = dataViewModel,
                        navDrawerViewModel = navigationDrawerViewModel,
                        selectArtMobilityVM = selectArtMobilityVM,
                        getProCaisseDataViewModel = getProCaisseDataViewModel,
                        locationViewModule = locationViewModule,
                        networkViewModel = networkViewModel,
                        clientId = selectedClientCode?: "",
                        settingViewModel = settingViewModel,
                        proCaisseViewModels = proCaisseViewModels,
                        syncProcaisseViewModels = syncProcaisseViewModels
                    )
                }
            },
            /*  extraPane = {

                }*/
        )
    }

 }







