package com.asmtunis.procaisseinventory.pro_caisse.client.data.local.repository

import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.coroutines.flow.Flow

interface ClientRoomRepository {

    fun upsert(value: Client) // : Long

    fun upsertAll(value: List<Client>)

    // suspend fun  delete(customEntity : CustomEntity)
    fun delete(value: Client)

    fun deleteAll()
    fun updateSyncClient(codeM: String, code: String)

    fun updateSoldClient(codeClt: String, soldClient: String)
    fun updateMoneyClient(codeClt: String, soldClient: String, cliCredit: String, cliDebit: String)
    fun getAll(): Flow<List<Client>?>
    fun count(): Flow<Int>
    fun getNotSync(): Flow<List<Client>?>

    fun getOneByCode(code: String): Flow<Client?>


    fun filterByName(filterString: String, sortBy: String?, sold: Int?, isAsc: Int?, filterType : String, filterByClientEtat: String): Flow<List<Client>>
    fun filterByCLICode(filterString: String, sortBy: String?, sold: Int?, isAsc: Int?, filterType : String, filterByClientEtat: String): Flow<List<Client>>
    fun filterByCLIMat(filterString: String, sortBy: String?, sold: Int?, isAsc: Int?, filterType : String, filterByClientEtat: String): Flow<List<Client>>

    fun getAllFiltred(isAsc: Int?, sortBy: String?, sold: Int?, filterType : String, filterByClientEtat: String): Flow<List<Client>>
}
