package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.dao.EtatOrdreMissionDAO
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.repository.EtatOrdreMissionLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.repository.EtatOrdreMissionLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.dao.LigneOrdreMissionDAO
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.repository.LigneOrdreMissionLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.repository.LigneOrdreMissionLocalRepositoryImpl
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.dao.OrdreMissionDAO
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.repository.OrdreMissionLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.repository.OrdreMissionLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
    @InstallIn(SingletonComponent::class)
    class OrdreMissionLocalModule {

        @Provides
        @Singleton
        fun provideOrdreMissionDao(
            proCaisseDataBase: ProCaisseDataBase
        ) = proCaisseDataBase.ordreMissionDAO()

        @Provides
        @Singleton
        @Named("OrdreMission")
        fun provideOrdreMissionRepository(
            ordreMissionDAO: OrdreMissionDAO
        ): OrdreMissionLocalRepository = OrdreMissionLocalRepositoryImpl(ordreMissionDAO = ordreMissionDAO)



    @Provides
    @Singleton
    fun provideLigneOrdreMissionDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.ligneOrdreMissionDAO()

    @Provides
    @Singleton
    @Named("LigneOrdreMission")
    fun provideLigneOrdreMissionRepository(
        ligneOrdreMissionDAO: LigneOrdreMissionDAO
    ): LigneOrdreMissionLocalRepository = LigneOrdreMissionLocalRepositoryImpl(ligneOrdreMissionDAO = ligneOrdreMissionDAO)



    @Provides
    @Singleton
    fun provideEtatOrdreMissionDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.etatOrdreMissionDAO()

    @Provides
    @Singleton
    @Named("EtatOrdreMission")
    fun provideEtatOrdreMissionRepository(
        etatOrdreMissionDAO: EtatOrdreMissionDAO
    ): EtatOrdreMissionLocalRepository = EtatOrdreMissionLocalRepositoryImpl(etatOrdreMissionDAO = etatOrdreMissionDAO)

}