
package com.asmtunis.procaisseinventory.pro_caisse.dashboard.stat.data

import androidx.compose.runtime.Immutable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import com.asmtunis.procaisseinventory.core.UiText

/* Hard-coded data for the Rally sample. */

@Immutable
data class Account(
    val name: String,
    val number: Int,
    val balance: Float,
    val color: Color
)


@Immutable
data class ChartData(
    val title: UiText,
    val description: UiText,
   val pieChartData: List<PieChartSliceData>
)


data class PieChartSliceData(
    val name: UiText,
    val amount: Double,
    val radius: Dp,
    val color: Color,
)


