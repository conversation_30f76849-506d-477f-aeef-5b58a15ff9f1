package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.BON_RETOUR_TABLE)
@Serializable
data class BonRetour  (
    @PrimaryKey(autoGenerate = true)
    @Transient
    val id: Long = 0,


    //@PrimaryKey
    @ColumnInfo(name = "BOR_Numero")
    @SerialName("BOR_Numero")
    
    var bORNumero: String = "",

    @ColumnInfo(name = "BOR_date")
    @SerialName("BOR_date")
    
    var bORDate: String? = "",

    @ColumnInfo(name = "BOR_codefrs")
    @SerialName("BOR_codefrs")
    var bORCodefrs: String? = "",

    @ColumnInfo(name = "BOR_nomfrs")
    @Transient
    var bORNomfrs: String? = "",

    @ColumnInfo(name = "BOR_Exercice")
    @SerialName("BOR_Exercice")
    
    var bORExercice: String? = "",

    @ColumnInfo(name = "BOR_NumBE")
    @SerialName("BOR_NumBE")
    
    var bORNumBE: String? = "",

    @ColumnInfo(name = "BOR_Mnt_HT")
    @SerialName("BOR_Mnt_HT")
    
    var bORMntHT: String? = "",

    @ColumnInfo(name = "BOR_Mnt_Fodec")
    @SerialName("BOR_Mnt_Fodec")
    
    var bORMntFodec: String? = "",

    @ColumnInfo(name = "BOR_Mnt_Remise")
    @SerialName("BOR_Mnt_Remise")
    
    var bORMntRemise: String? = "",

    @ColumnInfo(name = "BOR_Mnt_MntNetHt")
    @SerialName("BOR_Mnt_MntNetHt")
    
    var bORMntMntNetHt: String? = "",

    @ColumnInfo(name = "BOR_Mnt_Tva")
    @SerialName("BOR_Mnt_Tva")
    
    var bORMntTva: String? = "",

    @ColumnInfo(name = "BOR_Mnt_TTC")
    @SerialName("BOR_Mnt_TTC")
    
    var bORMntTTC: String? = "",

    @ColumnInfo(name = "BOR_Station")
    @SerialName("BOR_Station")
    
    var bORStation: String? = "",

    @ColumnInfo(name = "BOR_Type")
    @SerialName("BOR_Type")
    
    var bORType: String? = "",

    @ColumnInfo(name = "BOR_Session")
    @SerialName("BOR_Session")
    
    var bORSession: String? = "",

    @ColumnInfo(name = "BOR_Mnt_Achat")
    @SerialName("BOR_Mnt_Achat")
    
    var bORMntAchat: String? = "",

    @ColumnInfo(name = "Observation")
    @SerialName("Observation")
    
    var observation: String? = "",

    @ColumnInfo(name = "BON_ENT_MntFodec")
    @SerialName("BON_ENT_MntFodec")
    
    var bONENTMntFodec: String? = "",

    @ColumnInfo(name = "BON_ENT_MntDC")
    @SerialName("BON_ENT_MntDC")
    var bONENTMntDC: String? = "",



): BaseModel() {
    @Ignore
    val bORDateFormatted = this.bORDate?.substringBefore(".")
}
