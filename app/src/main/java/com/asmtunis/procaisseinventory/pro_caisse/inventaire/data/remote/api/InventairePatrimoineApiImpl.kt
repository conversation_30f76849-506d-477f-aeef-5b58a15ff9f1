package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.data.image_piece_joint.domaine.ImagePieceJoint
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.InvPatBatchResponse
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.AddPieceJointInventaire
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.ControlInventaireResponse
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class InventairePatrimoineApiImpl(private val client: HttpClient) : InventairePatrimoineApi {


    override suspend fun addBatchInvPat(
        baseConfig: String
    ): Flow<DataResult<List<InvPatBatchResponse>>> = flow {

        val result = executePostApiCall<List<InvPatBatchResponse>>(
            client = client,
            endpoint = Urls.ADD_BATCH_BON_COMMANDE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun controlInventaire(baseConfig: String): Flow<DataResult<ControlInventaireResponse>> = flow{

        val result = executePostApiCall<ControlInventaireResponse>(
            client = client,
            endpoint = Urls.CONTROLE_INVENTAIRE,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchPiecJointInventaire(baseConfig: String): Flow<DataResult<List<AddPieceJointInventaire>>> = flow {

        val result = executePostApiCall<List<AddPieceJointInventaire>>(
            client = client,
            endpoint = Urls.UPLOAD_IMAGES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getPiecesJointInventaire(baseConfig: String): Flow<DataResult<List<ImagePieceJoint>>> = flow {


        val result = executePostApiCall<List<ImagePieceJoint>>(
            client = client,
            endpoint = Urls.GET_IMAGES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }