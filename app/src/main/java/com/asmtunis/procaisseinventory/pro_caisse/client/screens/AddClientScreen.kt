package com.asmtunis.procaisseinventory.pro_caisse.client.screens

import android.app.Activity.RESULT_OK
import android.util.Log
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.twotone.ArrowBack
import androidx.compose.material.icons.filled.Email
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.twotone.Map
import androidx.compose.material.icons.twotone.Save
import androidx.compose.material3.Button
import androidx.compose.material3.Checkbox
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.asmtunis.countrycodepicker.data.utils.getNumberHint
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.PROSPECT
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.connectivity.location.CheckLocationSetting.checkLocationSetting
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationViewModule
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.client.text_validation.AddClientFormEvent
import com.asmtunis.procaisseinventory.pro_caisse.client.text_validation.ValidationAddClientEvent
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.PhoneCountryPicker
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.ProCaisseViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi
import com.google.android.gms.maps.model.CameraPosition
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.compose.GoogleMap
import com.google.maps.android.compose.MapProperties
import com.google.maps.android.compose.MapType
import com.google.maps.android.compose.MapUiSettings
import com.google.maps.android.compose.MarkerInfoWindowContent
import com.google.maps.android.compose.MarkerState
import com.google.maps.android.compose.rememberCameraPositionState
import com.simapps.ui_kit.dialogues.CustomAlertDialogue
import com.simapps.ui_kit.drop_down_menu.GenericDropdownMenu
import com.simapps.ui_kit.edit_text.EditTextField
import java.util.Locale

@OptIn(ExperimentalPermissionsApi::class, ExperimentalMaterial3Api::class)
@Composable
fun AddClientScreen(
    navigate: (route: String) -> Unit,
    popBackStack: () -> Unit,
    clientId: String,
    settingViewModel: SettingViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    dataViewModel: DataViewModel,
    networkViewModel: NetworkViewModel,
    mainViewModel: MainViewModel,
    locationViewModule: LocationViewModule,
    navigationDrawerViewModel: NavigationDrawerViewModel,
    proCaisseViewModels: ProCaisseViewModels
) {
    val textValidationViewModel = proCaisseViewModels.clientTextValidationViewModel
     val clientViewModel = proCaisseViewModels.clientViewModel
    // TODO when add the possibility to choose client position from map check this authorisation 109585132

    val stateAddClient = textValidationViewModel.stateAddClient

   val toaster = rememberToasterState()
   ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val customLocationAuthorisation = proCaisseAuthorization.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.CUSTOM_LOCATION }

   // val designationCustomLocationAuth = mainViewModel.authorisationList.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.CUSTOM_LOCATION }

    val scrollState = rememberScrollState()

    val location = locationViewModule.locationState

    val typeClientExpanded = clientViewModel.typeClientExpanded

    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig

    val settingResultRequest = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartIntentSenderForResult()
    ) { activityResult ->
        if (activityResult.resultCode == RESULT_OK) {
            Log.d("appDebug", "Accepted")
        } else {
            Log.d("appDebug", "Denied")
        }
    }

    val context = LocalContext.current

    val typeClientList = context.resources.getStringArray(R.array.clients_type_list)

    val utilisateur = mainViewModel.utilisateur

    val typeUtilisateur = utilisateur.typeUser


    val clientList = mainViewModel.clientList

    val clientByCode = clientList.firstOrNull { it.cLICode == clientId }?: Client()//mainViewModel.clientByCode

    val validationAddClientEvents = textValidationViewModel.validationAddClientEvents

    val latitude = location.latitude
    val longitude = location.longitude

    val cameraPositionState = rememberCameraPositionState {
        position = CameraPosition.fromLatLngZoom(
            LatLng(
                 latitude ?: 34.739330501022096,
                longitude ?: 10.75393363814977
            ),
            15f
        )
    }
    val uiSettings by remember { mutableStateOf(MapUiSettings()) }
    val properties by remember {
        mutableStateOf(
            MapProperties(
                isBuildingEnabled = true,
                isIndoorEnabled = true,
                isMyLocationEnabled = true,
                isTrafficEnabled = true,
                mapType = MapType.HYBRID
            )
        )
    }

    BackHandler(enabled = true) {
        textValidationViewModel.resetClientVariable()
        popBackStack()
    }
    LaunchedEffect(key1 = validationAddClientEvents) {
        clientViewModel.handleAddClientEvents(
            codeM = mainViewModel.codeM,
            exerciceList = mainViewModel.exerciceList,
            validationAddClientEvents = validationAddClientEvents,
            utilisateur = utilisateur,
            clientByCode = clientByCode,
            popBackStack = {
                textValidationViewModel.resetClientVariable()
                textValidationViewModel.onvalidationAddClientEventsChange(ValidationAddClientEvent())
                popBackStack()
                           }
        )
    }


    LaunchedEffect(key1 = location.latitude, key2 = location.longitude) {

        if (location.longitude != null) {
            textValidationViewModel.onAddClientEvent(AddClientFormEvent.LongitudeChanged(location.longitude.toString()))
            textValidationViewModel.onAddClientEvent(AddClientFormEvent.LatitudeChanged(location.latitude.toString()))

        }

        if (location.adresse != null) {
            textValidationViewModel.onAddClientEvent(AddClientFormEvent.AdresseChanged("${location.adresse.knownName}, ${location.adresse.city} ${location.adresse.state}, ${location.adresse.country}"))
            textValidationViewModel.onAddClientEvent(AddClientFormEvent.DelegationChanged(location.adresse.city))
            textValidationViewModel.onAddClientEvent(AddClientFormEvent.GouvernoratChanged(location.adresse.state))
        }


        //  locationViewModule.resetCurrentLocation()
    }



    BackHandler(true) {
        mainViewModel.onShowDismissScreenAlertDialogChange(true)
    }

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                title = if (clientByCode.cLICode == "") stringResource(R.string.add_client) else stringResource(R.string.modify_client), // stringResource(id = navigationViewModel.selectedMenu.title),
                showNavIcon = true,
            navIcon = Icons.AutoMirrored.TwoTone.ArrowBack,
            onNavigationClick = { mainViewModel.onShowDismissScreenAlertDialogChange(true) },
            )
        },
        //    containerColor = colorResource(id = R.color.black),
        floatingActionButton = {
            FloatingActionButton(
                onClick = {
               val clt = clientList.firstOrNull { it.cLIMatFisc == stateAddClient.matriculeFiscal }
               if(clt != null) {
                   showToast(
                       context = context,
                       toaster = toaster,
                       message = context.resources.getString(R.string.clt_existe, "${stateAddClient.matriculeFiscal} (${clt.cLINomPren}) "),
                       type =  ToastType.Error
                   )
                   return@FloatingActionButton
               }
                textValidationViewModel.onAddClientEvent(AddClientFormEvent.SubmitAddClient)
            }) {
                Icon(
                    imageVector = Icons.TwoTone.Save,
                    contentDescription = stringResource(id = R.string.cd_addClient_button)
                )
            }
        }
    ) { padding ->

        CustomAlertDialogue(
            title = context.getString(R.string.info),
            msg = context.getString(R.string.confirm_close_screen),
            openDialog = mainViewModel.showDismissScreenAlertDialog,
            confirmText = stringResource(id = R.string.oui),
            cancelText = stringResource(id = R.string.non),
            setDialogueVisibility = { mainViewModel.onShowDismissScreenAlertDialogChange(it) },
            customAction = {
                textValidationViewModel.resetClientVariable()
                popBackStack()
            }
        )

        if (clientViewModel.showMapView) {

            Dialog(
                onDismissRequest = {
                    clientViewModel.onShowMapViewChange(false)
                },
                properties =
                DialogProperties(
                    usePlatformDefaultWidth = false,
                ),
                content = {
                                GoogleMap(
                                    mergeDescendants = false,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .fillMaxHeight(0.8f),
                                    cameraPositionState = cameraPositionState,
                                    properties = properties,
                                    uiSettings = uiSettings,
                                    onMapClick = { latlng ->
                                        if(customLocationAuthorisation != null) {
                                            locationViewModule.getCurrentAdress(
                                                latitude = latlng.latitude,
                                                longitude = latlng.longitude
                                            )
                                            textValidationViewModel.onAddClientEvent(AddClientFormEvent.LatitudeChanged(latlng.latitude.toString()))

                                            textValidationViewModel.onAddClientEvent(AddClientFormEvent.LongitudeChanged(latlng.longitude.toString()))
                                        }
                                        else {
                                            clientViewModel.onShowMapViewChange(false)
                                            showToast(
                                                context = context,
                                                toaster = toaster,
                                                message = context.resources.getString(R.string.request_custom_auth, customLocationAuthorisation?.AutoDescription?:"Choisir l'emplacement du client manuellement sur la carte"),
                                                type =  ToastType.Error,
                                            )
                                        }
                                    }
                                ) {
                                    MarkerInfoWindowContent(
                                        state = remember {
                                            MarkerState(
                                                position = LatLng(
                                                    stringToDouble(stateAddClient.latitude),
                                                    stringToDouble(stateAddClient.longitude)
                                                )
                                            )
                                        },
                                        visible = true,
                                        title = stateAddClient.nom,
                                        snippet = stateAddClient.matriculeFiscal,
                                        onClick = {
                                            if (!it.isInfoWindowShown) it.showInfoWindow()
                                            else it.hideInfoWindow()

                                            false
                                        },

                                        onInfoWindowLongClick = {
                                           // onInfoWindowLongClick(clt)
                                        }
                                    )
                                }




                             /*   Spacer(modifier = Modifier.height(18.dp))
                            }

                    }*/
                },
            )


        }



        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            EditTextField(
                text = stateAddClient.matriculeFiscal,
                errorValue = stateAddClient.matriculeFiscalError?.asString(),
                label = stringResource(R.string.tax_registration_number_field_title),
                onValueChange = {
                    textValidationViewModel.onAddClientEvent(AddClientFormEvent.MatriculeFiscaleChanged(it))
                },
                readOnly = false,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Password,
                imeAction = ImeAction.Next
            )

            EditTextField(
                text = stateAddClient.nom,
                errorValue = stateAddClient.nomError?.asString(),
                label = stringResource(R.string.entitled_field_title),
                onValueChange = {
                    textValidationViewModel.onAddClientEvent(AddClientFormEvent.NomChanged(it))
                },
                readOnly = false,
                enabled = true,
                leadingIcon = Icons.Default.Person,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            // TODO verify if when select type client passager DO I NEED TO REMOVE CORRDINATES ?
            GenericDropdownMenu(
                modifier = Modifier.fillMaxWidth(0.95f),
                designation = stateAddClient.typeClient,
                errorValue = stateAddClient.typeClientError?.asString(),
                itemExpanded = typeClientExpanded,
                label = stringResource(R.string.type_client),
                itemList = typeClientList.toList(),
                selectedItem = stateAddClient.typeClient,
                getItemDesignation = { it },
                readOnly = typeUtilisateur.lowercase(Locale.getDefault()) != PROSPECT.lowercase(Locale.getDefault()),
                onClick = {
                    textValidationViewModel.onAddClientEvent(AddClientFormEvent.TypeClientChanged(it))
                    clientViewModel.onTypeClientExpandedChange(false)
                },
                 onItemExpandedChange = { clientViewModel.onTypeClientExpandedChange(it) },
                lottieAnimEmpty = { LottieAnim(lotti = R.raw.emptystate) },
                lottieAnimError = { LottieAnim(lotti = R.raw.connection_error, size = it) }
            )


            PhoneCountryPicker(
                errorValue = stateAddClient.phone1Error,
                countryCode = stateAddClient.countryData1.countryCode.uppercase(),
                stringId = R.string.phone1_field_title,
                value = stateAddClient.phone1,
                onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.PhoneNumber1Changed(it)) },
                onCountryChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.CountryData1Changed(it)) },
                hint = getNumberHint(stateAddClient.countryData1.countryCode),
                imeAction = ImeAction.Next
            )

            PhoneCountryPicker(
                errorValue = stateAddClient.phone2Error,
                countryCode = stateAddClient.countryData2.countryCode.uppercase(),
                onCountryChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.CountryData2Changed(it)) },
                stringId = R.string.phone2_field_title,
                value = stateAddClient.phone2,
                onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.PhoneNumber2Changed(it)) },
                hint = getNumberHint(stateAddClient.countryData1.countryCode),
                imeAction = ImeAction.Next
            )



            EditTextField(
                text = stateAddClient.email,
                errorValue = stateAddClient.emailError?.asString(),
                label = stringResource(R.string.email_field_title),
                onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.EmailChanged(it)) },
                readOnly = false,
                enabled = true,
                leadingIcon = Icons.Default.Email,
                keyboardType = KeyboardType.Email,
                imeAction = ImeAction.Next
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 12.dp, end = 12.dp),
                horizontalArrangement = Arrangement.spacedBy(6.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                EditTextField(
                    modifier = Modifier.fillMaxWidth(0.45f),
                    text = stateAddClient.proffession,
                    errorValue = stateAddClient.proffessionError?.asString(),
                    label = stringResource(R.string.profession_field_title),
                    onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.ProffessionChanged(it)) },
                    readOnly = false,
                    enabled = true,
                    leadingIcon = Icons.Default.Home,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                )
                // Spacer(modifier = Modifier.width(6.dp))
                EditTextField(
                    modifier = Modifier.fillMaxWidth(),
                    text = stateAddClient.nomSociete,
                    errorValue = stateAddClient.nomSocieteError?.asString(),
                    label = stringResource(R.string.company_field_title),
                    onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.NomsocieteChanged(it)) },
                    readOnly = false,
                    enabled = true,
                    leadingIcon = Icons.Default.Home,
                    keyboardType = KeyboardType.Text,
                    imeAction = ImeAction.Next
                )
            }

            EditTextField(
                text = stateAddClient.addresse,
                errorValue = stateAddClient.addresseError?.asString(),
                label = stringResource(R.string.adresse_field_title),
                onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.AdresseChanged(it)) },
                readOnly = false,
                enabled = true,
                showTrailingIcon = true,
                leadingIcon = Icons.Default.LocationOn,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )
            EditTextField(
                text = stateAddClient.gouvernorat,
                errorValue = stateAddClient.gouvernoratError?.asString(),
                label = stringResource(R.string.gouvernorats),
                onValueChange = {
                    textValidationViewModel.onAddClientEvent(AddClientFormEvent.GouvernoratChanged(it))
                },
                readOnly = false,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            EditTextField(
                text = stateAddClient.delegation,
                errorValue = stateAddClient.delegationError?.asString(),
                label = stringResource(R.string.delegations),
                onValueChange = {
                    textValidationViewModel.onAddClientEvent(AddClientFormEvent.DelegationChanged(it))
                },
                readOnly = false,
                enabled = true,
                leadingIcon = Icons.Default.Home,
                keyboardType = KeyboardType.Text,
                imeAction = ImeAction.Next
            )

            AskPermission(
                permission = listOf(
                    android.Manifest.permission.ACCESS_COARSE_LOCATION,
                    android.Manifest.permission.ACCESS_FINE_LOCATION
                ),
                permissionNotAvailableContent = { permissionState ->
                    Column(
                        modifier = Modifier
                            //  .background(colorResource(id = R.color.black))
                            .fillMaxSize()
                            .padding(padding),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        LottieAnim(lotti = R.raw.emptystate)
                        Spacer(modifier = Modifier.height(16.dp))

                        val textToShow = if (permissionState.shouldShowRationale) {
                            stringResource(R.string.access_gps_request_permession)
                        } else {
                            stringResource(R.string.gps_not_available)
                        }
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(textToShow)
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(onClick = { permissionState.launchMultiplePermissionRequest() }) {
                            Text(stringResource(R.string.request_gps_auth))
                        }
                    }
                },
                content = {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(start = 12.dp, end = 12.dp),
                        horizontalArrangement = Arrangement.spacedBy(6.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        EditTextField(
                            modifier = Modifier.fillMaxWidth(0.45f),
                            text = stateAddClient.longitude,
                            errorValue = stateAddClient.longitudeError?.asString(),
                            label = stringResource(R.string.Longitude),
                            onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.LongitudeChanged(it)) },
                            readOnly = true,
                            enabled = true,
                            leadingIcon = Icons.Default.LocationOn,
                            keyboardType = KeyboardType.Number,
                            imeAction = ImeAction.Next
                        )

                        EditTextField(
                            modifier = Modifier.fillMaxWidth(),
                            text = stateAddClient.latitude,
                            errorValue = stateAddClient.latitudeError?.asString(),
                            label = stringResource(R.string.latitude),
                            onValueChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.LatitudeChanged(it)) },
                            readOnly = true,
                            enabled = true,
                            leadingIcon = Icons.Default.LocationOn,
                            keyboardType = KeyboardType.Number,
                            imeAction = ImeAction.Next
                        )
                    }

                    locationViewModule.locationState.error?.let { error ->
                        Text(
                            text = error.asString()?: "Unknown GPS error",
                            color = Color.Red,
                            textAlign = TextAlign.Center
                        )
                    }




                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceEvenly,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        LottieAnim(
                            lotti = if (location.isLoading) R.raw.loading else R.raw.location_pin,
                           size = 30.dp,
                            onClick = {
                                checkLocationSetting(
                                    context = context,
                                    onDisabled = { intentSenderRequest ->
                                        settingResultRequest.launch(intentSenderRequest)
                                    },
                                    onEnabled = { locationViewModule.getCurrentLocation() }
                                )
                            }
                        )

                      //  Spacer(modifier = Modifier.width(12.dp))




    IconButton(
        onClick = { clientViewModel.onShowMapViewChange(true) }
    ) {
        Icon(
            imageVector = Icons.TwoTone.Map,
            contentDescription = stringResource(id = R.string.cd_addClient_button)
        )
    }


                    }
                }
            )


            Row(
                horizontalArrangement = Arrangement.SpaceEvenly,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .weight(1f)
                        .padding(8.dp)
                ) {
                    Checkbox(
                        checked = stateAddClient.haveTimbre,
                        onCheckedChange = {
                            textValidationViewModel.onAddClientEvent(AddClientFormEvent.HaveTimbreChanged(it))
                        }
                    )
                    Text(text = context.getString(R.string.stamp_field_title))
                }

                Row(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .weight(1f)
                        .padding(8.dp)
                ) {
                    Checkbox(
                        checked = stateAddClient.haveCredit,
                        onCheckedChange = { textValidationViewModel.onAddClientEvent(AddClientFormEvent.HaveCreditChanged(it)) }
                    )
                    Text(text = context.getString(R.string.credit_title))
                }
            }
            Spacer(modifier = Modifier.height(75.dp))
        }
    }
}



