package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.etat_ordre_mission.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import kotlinx.coroutines.flow.Flow


@Dao
interface EtatOrdreMissionDAO {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(etatOrdreMissionList: List<EtatOrdreMission>)

    @get:Query("select * from ${ProCaisseConstants.ETAT_ORDRE_MISSION_TABLE}")
    val all: Flow<List<EtatOrdreMission>?>

    @Query("select * from ${ProCaisseConstants.ETAT_ORDRE_MISSION_TABLE} where CodeEtatOrd = :code")
    fun getOne(code: String?): Flow<EtatOrdreMission?>

    @Query("DELETE FROM ${ProCaisseConstants.ETAT_ORDRE_MISSION_TABLE}")
    fun deleteAll()
}