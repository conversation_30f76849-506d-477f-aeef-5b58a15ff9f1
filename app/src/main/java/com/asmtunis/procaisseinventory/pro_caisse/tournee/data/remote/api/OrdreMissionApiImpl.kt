package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.remote.api

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.ChangeLigneOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithLines
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class OrdreMissionApiImpl(private val client: HttpClient) : OrdreMissionApi {
    override suspend fun getOrdreMissionWithLines(baseConfig: String): Flow<DataResult<List<OrdreMissionWithLines>>> = flow {

        val result = executePostApiCall<List<OrdreMissionWithLines>>(
            client = client,
            endpoint = Urls.GET_ORDRE_MISSION_WITH_LINES,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun getEtatOrdreMission(baseConfig: String): Flow<DataResult<List<EtatOrdreMission>>> = flow {

        val result = executePostApiCall<List<EtatOrdreMission>>(
            client = client,
            endpoint = Urls.GET_ETAT_ORDRE_MISSION,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun updateLigneOrdreMission(baseConfig: String): Flow<DataResult<Boolean>> = flow {

        val result = executePostApiCall<Boolean>(
            client = client,
            endpoint = Urls.UPDATE_LIGNE_ORDRE_MISSION,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addOrdreMission(baseConfig: String): Flow<DataResult<Boolean>> = flow {
        val result = executePostApiCall<Boolean>(
            client = client,
            endpoint = Urls.ADD_ORDRE_MISSION,
            baseConfig = baseConfig
        )
        emitAll(result)
    }

    override suspend fun batchUpdateLigneOrdreMission(baseConfig: String): Flow<DataResult<List<ChangeLigneOrdreMission>>> = flow {
        val result = executePostApiCall<List<ChangeLigneOrdreMission>>(
            client = client,
            endpoint = Urls.BATCH_UPDATE_ORDRE_MISSION,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }