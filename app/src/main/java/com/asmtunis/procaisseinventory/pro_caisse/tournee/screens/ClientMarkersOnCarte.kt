package com.asmtunis.procaisseinventory.pro_caisse.tournee.screens

import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.LocalContext
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.connectivity.location.LocationUtils
import com.asmtunis.procaisseinventory.core.utils.StringUtils
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.EtatOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMissionWithClient
import com.asmtunis.procaisseinventory.pro_caisse.tournee.view_model.TourneeViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import kotlin.math.abs

@Composable
fun ClientMarkersOnCarte(
    toaster: ToasterState,
    tourneeViewModel: TourneeViewModel,
    mainViewModel: MainViewModel,
    selectedLgOrdMissionWithCoordClient: List<LigneOrdreMissionWithClient>,
    cltListWithCoordonate: List<Client>,
    etatOrdreMission: List<EtatOrdreMission>,
    myCurrentLatitude: Double,
    myCurrentLongitude: Double,
    onInfoWindowLongClick: (Client) -> Unit
) {
    val context = LocalContext.current
    if (tourneeViewModel.showAllCltOnCarte) {
        ShowAllClientMarkerOnCarte(
            listClt = cltListWithCoordonate,
            onInfoWindowLongClick = {
                onInfoWindowLongClick(it)
            }
        )
    }

    else if (selectedLgOrdMissionWithCoordClient.isNotEmpty()) {
        ShowOrdreMissionClientsMarkerOnCarte(
            lgOrdMissionWithCoordClient = selectedLgOrdMissionWithCoordClient,
            onInfoWindowClick = {
                val distance = LocationUtils.getDistance(
                    lat1 = it.ligneOrdreMission.lIGOR_Latitude,
                    lon1 = it.ligneOrdreMission.lIGOR_Longitude,
                    myLat = myCurrentLatitude,
                    myLong = myCurrentLongitude
                )

                val nom = it.client?.cLINomPren ?: it.ligneOrdreMission.lIGORClt

                if (abs(distance) > LocationUtils.MAX_DISTANCE_TO_CLIENTS_IN_METERS) {

                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.getString(
                            R.string.must_be_near_client,
                            StringUtils.metersToKilometers(distance)
                        ) + " \n"+ context.getString(R.string.client_info, nom),
                        type =  ToastType.Warning,
                    )
                    return@ShowOrdreMissionClientsMarkerOnCarte
                }
                tourneeViewModel.onSelectedlgOrdMissionWithClt(it)
                tourneeViewModel.onMarkerClickedChange(true)
                mainViewModel.onSelectedClientChange(
                    it.client ?: Client(
                        cLINomPren = "",
                        cLICode = it.ligneOrdreMission.lIGORClt
                    )
                )

            },
            onInfoWindowLongClick = {

                if (it.client == null) {

                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.getString(R.string.code_client_non_disponible, it.ligneOrdreMission.lIGORClt),
                        type =  ToastType.Info,
                    )
                    return@ShowOrdreMissionClientsMarkerOnCarte
                }
                onInfoWindowLongClick(
                    it.client ?: Client(
                    cLINomPren = "",
                    cLICode = it.ligneOrdreMission.lIGORClt
                )
                )
            },
            etatOrdreMission = etatOrdreMission
        )
    }
}