package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.repository

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ligne_ordre_mission.dao.LigneOrdreMissionDAO
import kotlinx.coroutines.flow.Flow

class LigneOrdreMissionLocalRepositoryImpl(
        private val ligneOrdreMissionDAO: LigneOrdreMissionDAO
    ) : LigneOrdreMissionLocalRepository {
    override fun upsertAll(value: List<LigneOrdreMission>)  = ligneOrdreMissionDAO.insertAll(value)

    override fun upsert(value: LigneOrdreMission) = ligneOrdreMissionDAO.insert(value)
    override fun notSync(): Flow<List<LigneOrdreMission>?> = ligneOrdreMissionDAO.notSync
    override fun updateLgOrdreMissionNotSync(oRDCode: String)  = ligneOrdreMissionDAO.updateLgOrdreMissionNotSync(oRDCode)

    override fun deleteAll() = ligneOrdreMissionDAO.deleteAll()

}