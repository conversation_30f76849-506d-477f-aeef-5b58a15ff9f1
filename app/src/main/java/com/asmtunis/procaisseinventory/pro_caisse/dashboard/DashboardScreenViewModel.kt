@file:OptIn(SavedStateHandleSaveableApi::class, SavedStateHandleSaveableApi::class,
    SavedStateHandleSaveableApi::class
)

package com.asmtunis.procaisseinventory.pro_caisse.dashboard

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.domaine.ReglementPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import javax.inject.Inject


@HiltViewModel
    class DashboardScreenViewModel @Inject constructor(
        @IoDispatcher private val dispatcher: CoroutineDispatcher,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        savedStateHandle : SavedStateHandle
    ) : ViewModel() {



    @OptIn(SavedStateHandleSaveableApi::class)
    var showDetailVente by savedStateHandle.saveable { mutableStateOf(false) }
        private set

    fun onShowDetailVenteChange(value: Boolean) {
         showDetailVente = value
    }


    var reglementPayments by mutableStateOf(ReglementPayments())


      fun onCAChange(idSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getCA(idSCaisse = idSCaisse).collectLatest {
                reglementPayments = reglementPayments.copy(ca = it)
            }
        }
    }




    fun onBlNbrChange(idSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getTicketNumber(idSCaisse = idSCaisse).collectLatest {
                reglementPayments = reglementPayments.copy(nbrBl = it)
            }
        }
    }



      fun onListNTopClientsChange(idSCaisse: String?, number: Int) {

        viewModelScope.launch(dispatcher) {
            proCaisseLocalDb.bonLivraison.getNTopClientsBySCaisse(idSCaisse = idSCaisse, number = number).collectLatest { listClients ->
                reglementPayments = reglementPayments.copy(listNTopClients = listClients)
            }
        }
    }



    fun getReglementListBySession(station: String, idSCaisse: String, regRemarque: String) {
        viewModelScope.launch {
            proCaisseLocalDb.reglementCaisse.getBySession(station = station, caisse = idSCaisse).collectLatest { list->
             val listReglementBySession = list?: emptyList()

                reglementPayments = ReglementPayments(
                    mntEspece = listReglementBySession.sumOf { it.rEGCMntEspece?: 0.0 },
                    mntCheque = listReglementBySession.sumOf { it.rEGCMntCheque?: 0.0 },
                    mntTraite = listReglementBySession.sumOf { it.rEGCMntTraite?: 0.0 },
                    mntReglement = listReglementBySession.filter { it.rEGCRemarque == regRemarque }.sumOf { it.rEGCMontant?: 0.0 }
                )

            }
        }
    }


    fun onMntCreditChange(idSCaisse: String) {
        viewModelScope.launch {
            proCaisseLocalDb.bonLivraison.getMntCredit(caisse = idSCaisse).collectLatest {
                reglementPayments = reglementPayments.copy(mntCredit = it?: "0")
            }
        }
    }

}