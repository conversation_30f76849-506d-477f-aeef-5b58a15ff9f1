package com.asmtunis.procaisseinventory.pro_caisse.tournee.screens

import androidx.compose.runtime.Composable
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.compose.MarkerInfoWindow
import com.google.maps.android.compose.MarkerState

@Composable
fun ShowAllClientMarkerOnCarte(
    listClt: List<Client>,
    onInfoWindowLongClick: (Client) -> Unit
) {

    listClt.forEach { clt ->
        with(clt) {
            if(cltLatitude != null && cltLongitude != null) {
                MarkerInfoWindow(
                    state = MarkerState(
                        position = LatLng(
                            cltLatitude,
                            cltLongitude
                        )
                    ),
                    visible = true,
                    draggable = false,
                    title = cLINomPren,
                    snippet = cLIAdresse,
                    // tag = cLICode,
                    onClick = {
                        when {
                            !it.isInfoWindowShown -> it.showInfoWindow()
                            else -> it.hideInfoWindow()
                        }
                        // false to show direction button
                        false
                    },
                    // onInfoWindowClick = {
                    //    onInfoWindowLongClick(clt)
                    // },

                    onInfoWindowLongClick = {
                        onInfoWindowLongClick(clt)
                    },
                    content = { marker ->
                        CustomMarkerInfoWindow (marker = marker, client = clt)
                    }

                )
            }
        }

    }
}