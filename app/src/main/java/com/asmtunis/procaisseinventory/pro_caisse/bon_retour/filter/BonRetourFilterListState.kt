package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.filter

import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetourWithArticle
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.ListOrder
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.orderlist.util.OrderType
import com.asmtunis.procaisseinventory.shared_ui_components.searchview.search.ListSearch



data class BonRetourFilterListState(
    val lists: Map<BonRetourWithClient, List<LigneBonRetourWithArticle>> = emptyMap(),
    val listOrder: ListOrder = ListOrder.Date(OrderType.Descending),
    val search: ListSearch = ListSearch.FirstSearch()
)