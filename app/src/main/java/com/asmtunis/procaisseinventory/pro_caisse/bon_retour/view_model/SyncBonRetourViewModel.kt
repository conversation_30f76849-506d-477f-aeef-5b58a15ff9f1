package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.view_model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.domaine.RemoteResponseState
import com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction.DataStoreRepository
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.PROCAISSE_AUTO_SYNC_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.model.NestedItem
import com.asmtunis.procaisseinventory.core.utils.DefaultDispatcher
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainDispatcher
import com.asmtunis.procaisseinventory.core.utils.MainImmediateDispatcher
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetour
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.BonRetourWithClient
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement
import javax.inject.Inject


@HiltViewModel
class SyncBonRetourViewModel @Inject constructor(
    @DefaultDispatcher private val defaultDispatcher: CoroutineDispatcher,
    @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    @MainImmediateDispatcher private val mainImmediateDispatcher: CoroutineDispatcher,
    @MainDispatcher private val mainDispatcher: CoroutineDispatcher,
    private val proCaisseRemote: ProCaisseRemote,
    private val proCaisseLocalDb: ProCaisseLocalDb,
    private val listenNetwork: ListenNetwork,
    private val dataStoreRepository: DataStoreRepository,
    // app: Application
) : ViewModel() {
    private var autoSyncState  by mutableStateOf(false)
    private var autoSyncFlow = proCaisseLocalDb.dataStore.getBoolean(key = PROCAISSE_AUTO_SYNC_AUTHORISATION, default = true).distinctUntilChanged()
    private val networkFlow = listenNetwork.isConnected.distinctUntilChanged()

    private var  connected  by mutableStateOf(false)
    init {
        getNotSyncBonRetour()

    }


    var responseAddBonRetourState: RemoteResponseState<List<BonRetour>>  by mutableStateOf(RemoteResponseState())
        private set


    var bonRetourNotSync: Map<BonRetour, List<LigneBonRetour>> by mutableStateOf(emptyMap())
        private set
    var notSyncBonRetourObj : String by mutableStateOf("")
        private set


   private fun getNotSyncBonRetour() {
        viewModelScope.launch {
            val bonRetourNotSyncFlow =  proCaisseLocalDb.bonRetour.notSynced().distinctUntilChanged()


            combine(networkFlow, bonRetourNotSyncFlow, autoSyncFlow) { isConnected, bonRetourNotSyncList, autoSync ->
                connected = isConnected
                autoSyncState = autoSync
                bonRetourNotSyncList?.ifEmpty { emptyMap() }?: emptyMap()
            }.collect {

                if (it.isEmpty()) {
                    bonRetourNotSync = emptyMap()
                    return@collect
                }
                bonRetourNotSync = it
                if(connected && autoSyncState) syncBonRetour()
            }
        }
    }



    fun syncBonRetour(selectedBonRetour: BonRetourWithClient = BonRetourWithClient()) {
        viewModelScope.launch(dispatcherIO) {
           val listVisiteWithLinesDn = ArrayList<NestedItem<BonRetour,List<LigneBonRetour>>>()

            bonRetourNotSync.forEach { (key, value) ->
                run {
                    listVisiteWithLinesDn.add(
                        NestedItem(
                            parent  = key,
                            children  = value
                        )
                    )
                }
            }


            if(selectedBonRetour != BonRetourWithClient()) {
                listVisiteWithLinesDn.removeIf { it.parent != selectedBonRetour.bonRetour }
            }

            val baseConfigObj = GenericObject(
                proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()
                    ?.let { Json.decodeFromString(it) }?: BaseConfig(),
                Json.encodeToJsonElement(listVisiteWithLinesDn)
            )

            notSyncBonRetourObj = Json.encodeToString(baseConfigObj)
            proCaisseRemote.bonRetour.addBatchBonRetour(notSyncBonRetourObj).onEach { result ->
                when (result) {
                    is DataResult.Success -> {



                        for (i in result.data!!.indices) {
                            proCaisseLocalDb.bonRetour.setSynced(
                                bonRetourNum = result.data[i].bORNumero,
                                bonRetourNumM = listVisiteWithLinesDn[i].parent!!.bORNumero
                            )

                            proCaisseLocalDb.ligneBonRetour.setSynced(
                                newNum= result.data[i].bORNumero,
                                oldNum = listVisiteWithLinesDn[i].parent!!.bORNumero
                            )
                        }

                        responseAddBonRetourState = RemoteResponseState(data = result.data, loading = false, error = null)
                    }

                    is DataResult.Loading -> {
                        responseAddBonRetourState = RemoteResponseState(data = null, loading = true, error = null)
                    }

                    is DataResult.Error -> {
                        responseAddBonRetourState = RemoteResponseState(data = null, loading = false, error = result.message, message = selectedBonRetour.bonRetour?.bORNumero)
                    }
                }
            }.flowOn(dispatcherIO).launchIn(this)
        }
    }




    private fun updateClientMoney(
        codeClt: String,
        soldClient: String,
        CLICredit: String,
        CLIDebit: String
    ) {
        viewModelScope.launch(dispatcherIO) {
            proCaisseLocalDb.clients.updateMoneyClient(
                codeClt = codeClt,
                soldClient = soldClient,
                cliCredit = CLICredit,
                cliDebit = CLIDebit
            )
        }
    }
}


