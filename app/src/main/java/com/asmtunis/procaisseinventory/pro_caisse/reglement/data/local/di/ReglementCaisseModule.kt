package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.dao.ReglementCaisseDAO
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.respository.ReglementCaisseLocalRepository
import com.asmtunis.procaisseinventory.pro_caisse.reglement.data.local.respository.ReglementCaisseLocalRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
class ReglementCaisseModule {

    @Provides
    @Singleton
    fun provideReglementCaisseDao(
        proCaisseDataBase: ProCaisseDataBase
    ) = proCaisseDataBase.reglementCaisseDAO()

    @Provides
    @Singleton
    @Named("ReglementCaisse")
    fun provideReglementCaisseRepository(
        reglementCaisseDAO: ReglementCaisseDAO
    ): ReglementCaisseLocalRepository = ReglementCaisseLocalRepositoryImpl(reglementCaisseDAO = reglementCaisseDAO)


}