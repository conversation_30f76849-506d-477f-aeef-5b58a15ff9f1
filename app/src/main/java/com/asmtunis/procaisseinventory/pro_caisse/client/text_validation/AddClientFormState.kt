package com.asmtunis.procaisseinventory.pro_caisse.client.text_validation

import com.asmtunis.countrycodepicker.data.CountryData
import com.asmtunis.countrycodepicker.data.utils.getDefaultLangCode
import com.asmtunis.procaisseinventory.core.UiText
import java.util.Locale


data class AddClientFormState(
    val matriculeFiscal: String = "",
    val matriculeFiscalError: UiText? = null,


    val nom: String = "",
    val nomError: UiText? = null,

    val typeClient: String = "",
    val typeClientError: UiText? = null,

    val haveCredit: Boolean = false,
    val haveCreditError: UiText? = null,

    val haveTimbre: Boolean = false,
    val haveTimbreError: UiText? = null,

    val nomSociete: String = "",
    val nomSocieteError: UiText? = null,

    val email: String = "",
    val emailError: UiText? = null,



    val phone1: String = "",
    val phone1Error: UiText? = null,

    val phone2: String = "",
    val phone2Error: UiText? = null,


    val gouvernorat: String = "",
    val gouvernoratError: UiText? = null,

    val delegation: String = "",
    val delegationError: UiText? = null,


    val addresse: String = "",
    val addresseError: UiText? = null,


    val proffession: String = "",
    val proffessionError: UiText? = null,


    val longitude: String = "",
    val longitudeError: UiText? = null,

    val latitude: String = "",
    val latitudeError: UiText? = null,

    val countryData1: CountryData = CountryData(getDefaultLangCode().lowercase(Locale.getDefault())),
    val countryData2: CountryData = CountryData(getDefaultLangCode().lowercase(Locale.getDefault())),

    //  val countryData: CountryData = CountryData(getDefaultLangCode().lowercase(Locale.getDefault()), getDefaultPhoneCode(),"tn", R.drawable.tn),


)