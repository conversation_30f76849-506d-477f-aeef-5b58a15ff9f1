package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.Ticket
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable




@Serializable
data class ReglementCaisseWithTicketAndClient (
    @Embedded
    @SerialName("reglementCaisse")
    var reglementCaisse: ReglementCaisse? = null,

    @Relation(
        parentColumn = "REGC_NumTicket",
        entityColumn = "TIK_NumTicket"
    )
    @SerialName("ticket")
    var ticket: Ticket? = null,

    @Relation(
        parentColumn = "REGC_CodeClient",
        entityColumn = "CLI_Code"
    )
    @SerialName("client")
    var client: Client? = null,


    )