package com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.ui.adaptive_view

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material.icons.filled.HomeWork
import androidx.compose.material.icons.filled.WavingHand
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.articles.selection_ajout_patrimoine.domaine.SelectedPatrimoine
import com.asmtunis.procaisseinventory.data.marque.domaine.Marque
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.batiment.dep_out_byuser.data.domaine.DeplacementOutByUserWithImmobilisation
import com.asmtunis.procaisseinventory.shared_ui_components.tables.three_column.ThreeColumnTableWithImage
import com.simapps.ui_kit.custom_cards.ItemDetail

@Composable
fun DepOutByUserColumnView(
    articleMapByBarCode: Map<String, Article>,
    marqueList: List<Marque>,
    haveCamera: Boolean,
    padding: PaddingValues,
    deplacementOutByUser: DeplacementOutByUserWithImmobilisation,
    selectedPatrimoineList: List<SelectedPatrimoine>
    ) {

    Column(
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier =
        Modifier
            .fillMaxSize()
            .padding(padding),
    ) {

        ItemDetail(
            //  modifier = firstItemDetail.modifier,
            //  title = stringResource(id = R.string.societe),
            title = stringResource(id = R.string.batiment),
            dataText = deplacementOutByUser.immobilisation?.cLINomPren ?: deplacementOutByUser.deplacementOutByUser?.dEVCodeClient ?: "N/A",
            icon = Icons.Default.HomeWork
        )



        if(deplacementOutByUser.deplacementOutByUser?.dEVEtatBon == "1") {
            Spacer(modifier = Modifier.height(12.dp))

            ItemDetail(
                //  title = stringResource(id = R.string.societe),
                title = stringResource(id = R.string.etat),
                dataText = stringResource(id = R.string.en_instance),
                icon = Icons.Default.WavingHand,
                tint = MaterialTheme.colorScheme.error
            )
        }

        Spacer(modifier = Modifier.height(12.dp))
        ItemDetail(
            //  title = stringResource(id = R.string.societe),
            title = stringResource(id = R.string.date),
            dataText = deplacementOutByUser.deplacementOutByUser?.dEVDDm?.replace(".000", "")?.removeSuffix(" 00:00:00") ?: "",
            icon = Icons.Default.DateRange,
        )


        Spacer(modifier = Modifier.height(20.dp))

        ThreeColumnTableWithImage(
            articleMapByBarCode = articleMapByBarCode,
            haveCamera = haveCamera,
            marqueList = marqueList,
            selectedPatrimoineList = selectedPatrimoineList,
            onPress = {
                // TODO Maybe show more detail : TVA / DISCOUNT / . . .
            },
        )
    }
}