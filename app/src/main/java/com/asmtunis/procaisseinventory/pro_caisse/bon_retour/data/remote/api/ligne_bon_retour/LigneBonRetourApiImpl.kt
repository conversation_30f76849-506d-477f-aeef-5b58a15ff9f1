package com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.remote.api.ligne_bon_retour

import com.asmtunis.procaisseinventory.core.ktor.Urls
import com.asmtunis.procaisseinventory.core.ktor.executePostApiCall
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.bon_retour.data.domaine.LigneBonRetour
import io.ktor.client.HttpClient
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow


class LigneBonRetourApiImpl(private val client: HttpClient) : LigneBonRetourApi {
    override suspend fun getLigneBonRetours(baseConfig: String): Flow<DataResult<List<LigneBonRetour>>> = flow {
        val result = executePostApiCall<List<LigneBonRetour>>(
            client = client,
            endpoint = Urls.GET_LIGNE_BON_RETOUR,
            baseConfig = baseConfig
        )

        emitAll(result)
    }

    override suspend fun addBatchLigneBonRetours(baseConfig: String): Flow<DataResult<List<LigneBonRetour>>> = flow {

        val result = executePostApiCall<List<LigneBonRetour>>(
            client = client,
            endpoint = Urls.ADD_BATCH_LIGNE_BON_RETOUR,
            baseConfig = baseConfig
        )

        emitAll(result)
    }
    }