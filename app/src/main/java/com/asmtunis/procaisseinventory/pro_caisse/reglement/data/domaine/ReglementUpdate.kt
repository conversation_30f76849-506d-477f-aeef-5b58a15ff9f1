package com.asmtunis.procaisseinventory.pro_caisse.reglement.data.domaine

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ReglementUpdate (
    @SerialName("REGC_Code")
    var rEGC_Code: String? = null,

    @SerialName("tIKNumTicket")
    private var tIKIdCarnet: String? = null,

    @SerialName("REGC_Exercice")
    private var tIKExerc: String? = null,

    @SerialName("REGC_Code_M")
    var rEGC_Code_M: String? = null,

    @SerialName("message")
    var message: String? = null,

    @SerialName("REGC_IdSCaisse")
    var rEGC_IdSCaisse: String? = null,

    @SerialName("code")
    var code: String? = null,

    @SerialName("CodeClient")
    var codeClient: String? = null,

    @SerialName("SoldeClient")
    var soldeClient: String? = null,

    @SerialName("Debit")
    var debit: String? = null,

    @SerialName("Credit")
    var credit: String? = null
)
