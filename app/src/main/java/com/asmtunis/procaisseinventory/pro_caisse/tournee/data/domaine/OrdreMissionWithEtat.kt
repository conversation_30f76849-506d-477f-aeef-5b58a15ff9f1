package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Serializable
data class OrdreMissionWithEtat(
    @Embedded
    @SerialName("ordreMission")
    var ordreMission: OrdreMission? = null,

    @Relation(
        parentColumn = "ORD_etat",
        entityColumn = "CodeEtatOrd"
    )
    @SerialName("etatOrdreMission")
    var etatOrdreMission: EtatOrdreMission? = null,

    )