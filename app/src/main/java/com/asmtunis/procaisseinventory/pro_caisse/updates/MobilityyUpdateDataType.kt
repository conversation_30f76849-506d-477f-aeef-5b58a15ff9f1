package com.asmtunis.procaisseinventory.pro_caisse.updates


enum class MobilityyUpdateDataType(val displayName: String) {
    STATION_STOCK_ARTICLE("Update Station Stock Article"),
    TIMBRE("Update Timbre"),
    PRICE_PER_STATION("Update Price Per Station"),
    PARAMETRAGES("Update Parametrages"),
    ETABLISSEMENT("Update Etablissement"),
    BANQUES("Update Banques"),
    CARTE_RESTO("Update CarteResto"),
    DEVISES("Update Devises"),
    FAMILLE_DN("Update FamilleDn"),
    SUPERFICIE_DN("Update SuperficieDn"),
    TYPE_POINT_VENTE_DN("Update TypePointVenteDn"),
    TYPE_SERVICES_DN("Update TypeServicesDn"),
    VC_IMAGE("Update VCImage"),
    VC_TYPE_COMMUNICATION("Update VCTypeCommunication"),
    VC_LISTE_CONCURRENT("Update VCListeConcurrent"),
    VC_NEW_PRODUCT("Update VCNewProduct"),
    VC_AUTRE("Update VCAutre"),
    VC_PRIX("Update VCPrix"),
    VC_PROMO("Update VCPromo"),
    CLIENT("Update Client"),
    SESSION_CAISSES("Update SessionCaisses"),
    SESSION_CAISSES_BY_USER("Update Session Caisses By User"),
    VISITES_DN("Update VisitesDn"),
    LIGNES_VISITES_DN("Update LignesVisitesDn"),
    STATION("Update Station"),
    PREFIX("Update Prefix"),
    ARTICLES("Update Articles"),
    EXERCICE("Update Exercice")
}