package com.asmtunis.procaisseinventory.pro_caisse.dashboard.screen

import NavDrawer
import android.content.Intent
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.twotone.Sync
import androidx.compose.material.icons.twotone.Update
import androidx.compose.material3.AssistChipDefaults
import androidx.compose.material3.Badge
import androidx.compose.material3.BadgedBox
import androidx.compose.material3.DrawerValue
import androidx.compose.material3.ExtendedFloatingActionButton
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberDrawerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.OPERATEUR_PATRIMOINE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.DeplacementOutByUserRoute
import com.asmtunis.procaisseinventory.core.navigation.ProCaisseSyncRoute
import com.asmtunis.procaisseinventory.core.utils.ReplyNavigationType
import com.asmtunis.procaisseinventory.core.utils.Sync.getProCaisseTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.UpdateLoadingStateUtils
import com.asmtunis.procaisseinventory.nav_components.ID
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel.Companion.proCaisseDrawerItems
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.network_errors.view_model.NetworkErrorsViewModel
import com.asmtunis.procaisseinventory.pro_caisse.dashboard.DashboardScreenViewModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.buttons.ErrorGetDataButton
import com.asmtunis.procaisseinventory.shared_ui_components.payment_methods.PaymentConst.CREDIT_PAYEMENT_REMARK
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.rememberToasterState
import kotlinx.coroutines.launch

@Composable
fun DashboardScreen(
    navigate: (route: Any) -> Unit,
    networkErrorsVM: NetworkErrorsViewModel,
    intent: Intent,
    settingViewModel: SettingViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    mainViewModel: MainViewModel,
    //  procaisseViewModels: ProCaisseViewModels,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    networkViewModel: NetworkViewModel,
    dashboardScreenVM: DashboardScreenViewModel,


    syncInventoryViewModel: SyncInventoryViewModel,

    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels
) {
    // val dashboardScreenVM = procaisseViewModels.dashboardScreenVM

    val uiWindowState = settingViewModel.uiWindowState

    val context = LocalContext.current
    val isConnected = networkViewModel.isConnected

    val drawer = rememberDrawerState(initialValue = DrawerValue.Closed)

    val proCaisseAuthorization = getProCaisseDataViewModel.authorizationList

    val haveBLAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.BL }

    val haveClotureSessionAutoAuthorisation = proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.CLOT_SESSION_AUTO }
    val exerciceCode = mainViewModel.exerciceList.firstOrNull()?.exerciceCode?: ""
    val scope = rememberCoroutineScope()
    val density = LocalDensity.current
    val sCIdSCaisse = navDrawerViewModel.sessionCaisse.sCIdSCaisse

    val utilisateur = mainViewModel.utilisateur

    val station = utilisateur.Station


    val selectedBaseconfig: BaseConfig = dataViewModel.selectedBaseConfig


    val networkErrorsList = networkErrorsVM.networkErrorsList

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val selectedBaseConfig = dataViewModel.selectedBaseConfig

    val isLoadingProCaisseData = UpdateLoadingStateUtils.isLoadingProCaisseData(getProCaisseDataViewModel = getProCaisseDataViewModel)
    val isLoadingSharedData = UpdateLoadingStateUtils.isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)
    val isLoadingCommenSharedData = UpdateLoadingStateUtils.isLoadingCommenSharedData(getSharedDataViewModel = getSharedDataViewModel)


    LaunchedEffect(key1 = Unit) {
        if (intent.getBooleanExtra(Globals.FROM_NOTIFICATION, false)) {
            getProCaisseDataViewModel.getDeplacementOutByUser(
                baseConfig = selectedBaseconfig,
                listImmobilisation = mainViewModel.immobilisationList
            )

            proCaisseDrawerItems.find { it.id == ID.DEPLACEMENT_OUT_BYUSER_ID }?.let { navDrawerViewModel.onSelectedMenuChange(it) }

            navigate(DeplacementOutByUserRoute)
        }

        mainViewModel.onSelectedBaseconfigChange(baseConfig = selectedBaseConfig)
    }

    LaunchedEffect(key1 = station, key2 = sCIdSCaisse) {
        mainViewModel.onSelectedBaseconfigChange(baseConfig = selectedBaseConfig)
        dashboardScreenVM.onListNTopClientsChange(idSCaisse = sCIdSCaisse, number = 3)
        dashboardScreenVM.onCAChange(idSCaisse = sCIdSCaisse)
        dashboardScreenVM.onBlNbrChange(idSCaisse = sCIdSCaisse)


        dashboardScreenVM.getReglementListBySession(
            idSCaisse = sCIdSCaisse,
            station = station,
            regRemarque = CREDIT_PAYEMENT_REMARK
        )
        dashboardScreenVM.onMntCreditChange(idSCaisse = sCIdSCaisse)
    }

    val noSyncCount = getProCaisseTotalNoSyncCount(syncProcaisseViewModels = syncProcaisseViewModels,) + getSharedTotalNoSyncCount(syncSharedViewModels = syncSharedViewModels,)

    NavDrawer(
        navigate = { navigate(it) },
        drawer = drawer,
        navDrawerViewmodel = navDrawerViewModel,
        getProCaisseDataViewModel = getProCaisseDataViewModel,
        getProInventoryDataViewModel = getProInventoryDataViewModel,
        getSharedDataViewModel = getSharedDataViewModel,
        networkViewModel = networkViewModel,
        dataViewModel = dataViewModel,
        mainViewModel = mainViewModel,

        syncInventoryViewModel = syncInventoryViewModel,
        syncSharedViewModels = syncSharedViewModels,
        syncProcaisseViewModels = syncProcaisseViewModels,
        settingViewModel = settingViewModel
    ) {
        Scaffold(
            topBar = {
                AppBar(
                    showNavIcon = uiWindowState.navigationType == ReplyNavigationType.NAVIGATION_DRAWER,
                    baseConfig = selectedBaseconfig,
                    isConnected = networkViewModel.isConnected,
                    onNavigationClick = { scope.launch { drawer.open() } },
                    title = stringResource(id = navDrawerViewModel.proCaisseSelectedMenu.title),
                )
            },
            //    containerColor = colorResource(id = R.color.black),
            floatingActionButton = {
                Column(
                    verticalArrangement = Arrangement.Bottom,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // TODO IMPLEMENT SAME FUN FOR INV AND PRO CAISSE

                    if (noSyncCount == 0 && isConnected /*&&
                                !isLoadingProCaisseData(getProCaisseDataViewModel = getProCaisseDataViewModel) &&
                                !isLoadingSharedData(getSharedDataViewModel = getSharedDataViewModel)*/
                    ) {
                        FloatingActionButton(
                            onClick = {
                                scope.launch {
                                    if (isLoadingProCaisseData || isLoadingSharedData ||  isLoadingCommenSharedData) {
                                        return@launch
                                    }
                                    networkErrorsVM.deleteNetworkErrorsList()
                                    getProCaisseDataViewModel.getProcaisseData(
                                        baseConfig = selectedBaseconfig,
                                        utilisateur = utilisateur,
                                        exerciceCode = exerciceCode
                                    )

                                    getSharedDataViewModel.getSharedData(
                                        baseConfig = selectedBaseconfig,
                                        utilisateur = utilisateur,
                                    )

                                    getSharedDataViewModel.getCommenSharedData(baseConfig = selectedBaseconfig)
                                }

                            },
                        ) {
                            if (isLoadingProCaisseData || isLoadingSharedData || isLoadingCommenSharedData) {
                                LottieAnim(lotti = R.raw.loading, size = 25.dp)
                            } else {
                                //  Text(text = " Update ")
                                Icon(
                                    imageVector = Icons.TwoTone.Update,
                                    contentDescription = stringResource(id = R.string.update_db_title),
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(12.dp))

                    if (noSyncCount > 0) {
                        ExtendedFloatingActionButton(
                            onClick = {
                                navigate(ProCaisseSyncRoute)
                            },
                        ) {
                            Text(text = stringResource(id = R.string.sync_title))
                            Spacer(modifier = Modifier.width(9.dp))
                            BadgedBox(badge = { Badge { Text(noSyncCount.toString()) } }) {
                                Icon(
                                    imageVector = Icons.TwoTone.Sync,
                                    contentDescription = stringResource(id = R.string.sync_title),
                                    modifier = Modifier.size(AssistChipDefaults.IconSize),
                                )
                            }
                            Spacer(modifier = Modifier.width(9.dp))
                        }
                    }
                }
            },
        ) { padding ->
            Column(
                modifier =
                    Modifier
                        .fillMaxSize()
                        .verticalScroll(rememberScrollState())
                        .padding(padding)
                        .padding(start = 12.dp, end = 12.dp),
                verticalArrangement = Arrangement.Top,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {




                ErrorGetDataButton(
                    navigate = { navigate(it) },
                    networkErrorsList = networkErrorsList
                )

                if (utilisateur.typeUser != OPERATEUR_PATRIMOINE) {
                    VendorDashBoard(
                        navigateTo = { navigate(it) },
                        montantTotalStck = mainViewModel.montantTotalStck,
                        dashboardScreenVM = dashboardScreenVM,
                        toaster = toaster,
                        haveBLAuthorisation = haveBLAuthorisation
                    )
                } else {
                    PatrimoineDashBoard(
                        navDrawerViewModel = navDrawerViewModel,
                        utilisateur = utilisateur,
                        exerciceCode = exerciceCode,
                        logo = mainViewModel.logo,
                        haveClotureSessionAutoAuthorisation = haveClotureSessionAutoAuthorisation
                    )
                }
            }
        }
    }
}












