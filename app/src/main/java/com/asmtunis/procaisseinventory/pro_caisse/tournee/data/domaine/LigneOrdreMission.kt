package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine

import androidx.room.ColumnInfo
import androidx.room.Entity
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.core.model.BaseModel
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient


@Entity(tableName = ProCaisseConstants.ORDRE_MISSION_LIGNE_TABLE, primaryKeys = ["LIGOR_Code", "LIGOR_Exer", "LIGOR_Clt"])
@Serializable
data class LigneOrdreMission (
    @SerialName("LIGOR_Code")
    @ColumnInfo(name = "LIGOR_Code")
    
    var lIGORCode: String = "",

    @SerialName("LIGOR_Exer")
    @ColumnInfo(name = "LIGOR_Exer")
    
    var lIGORExer: String = "",

    @SerialName("LIGOR_Clt")
    @ColumnInfo(name = "LIGOR_Clt")
    
    var lIGORClt: String = "",

    @SerialName("LIGOR_Etat")
    @ColumnInfo(name = "LIGOR_Etat")
    
    var lIGOREtat: String = "",

    @SerialName("LIGOR_Note")
    @ColumnInfo(name = "LIGOR_Note")
    
    var lIGORNote: String? = null,

    @SerialName("export")
    @ColumnInfo(name = "export")
    
    var export: Int? = null,

    @SerialName("DDm")
    @ColumnInfo(name = "DDm")
    
    var dDm: String? = null,

    @SerialName("exportM")
    @ColumnInfo(name = "exportM")
    
    var exportM: Int? = null,

    @SerialName("DDmM")
    @ColumnInfo(name = "DDmM")
    
    var dDmM: String? = null,

    @SerialName("LIGOR_Ordre")
    @ColumnInfo(name = "LIGOR_Ordre")
    
    var lIGOROrdre: Int? = null,

    @SerialName("LIGOR_Latitude")
    @ColumnInfo(name = "LIGOR_Latitude")
    
    var lIGOR_Latitude: Double = 0.0,

    @SerialName("LIGOR_Longitude")
    @ColumnInfo(name = "LIGOR_Longitude")
    
    var lIGOR_Longitude: Double = 0.0,

    @SerialName("LIGOR_Date")
    @ColumnInfo(name = "LIGOR_Date")
    
    var lIGOR_Date: String? = null,

    /*@SerialName("infoM")
    @ColumnInfo(name = "infoM")
    var infoM: String? = null,*/

    @SerialName("LIG_NUMs_BL")
    @ColumnInfo(name = "LIG_NUMs_BL")

    var lIGOR_NUMs_BL: String? = null,


    
   @Transient
    var distance: Double = 0.0,
    @Transient
    var address: String? = null,
    @Transient
    var distanceKm: String? = null,
    @Transient
    var distanceTime: String? = null,

): BaseModel()
