package com.asmtunis.procaisseinventory.pro_caisse.client.data.remote.api

import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.coroutines.flow.Flow


interface ClientsApi {



    suspend fun getClients(baseConfig: String, cltEquivalent: String?): Flow<DataResult<List<Client>>>
    suspend fun addClients(baseConfig: String): Flow<DataResult<List<Client>>>
}