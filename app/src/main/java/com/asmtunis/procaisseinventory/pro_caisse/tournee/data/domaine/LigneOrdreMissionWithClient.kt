package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine

import androidx.room.Embedded
import androidx.room.Relation
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import kotlinx.serialization.Serializable




@Serializable
data class LigneOrdreMissionWithClient(
    @Embedded
    var ligneOrdreMission: LigneOrdreMission = LigneOrdreMission(),
    @Relation(
        parentColumn = "LIGOR_Clt",
        entityColumn = "CLI_Code"
    ) var client: Client? = null
)
