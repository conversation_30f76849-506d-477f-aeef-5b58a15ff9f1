package com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.repository

import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.LigneOrdreMissionWithClient
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMission
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.domaine.OrdreMissionWithEtat
import com.asmtunis.procaisseinventory.pro_caisse.tournee.data.local.ordre_mission.dao.OrdreMissionDAO
import kotlinx.coroutines.flow.Flow


class OrdreMissionLocalRepositoryImpl(
        private val ordreMissionDAO: OrdreMissionDAO
    ) : OrdreMissionLocalRepository {
        override fun upsertAll(value: List<OrdreMission>)  = ordreMissionDAO.insertAll(value)

        override fun upsert(value: OrdreMission) = ordreMissionDAO.insertOne(value)
    override fun updateOrdreMissionEtat(oRDCode: String, codeEtat: String) =
     ordreMissionDAO.updateOrdreMissionEtat(oRDCode = oRDCode, codeEtat = codeEtat)

    override fun getBydate(date: String): Flow<Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>?> = ordreMissionDAO.getBydate(date)
    override fun getAll(): Flow<Map<OrdreMissionWithEtat, List<LigneOrdreMissionWithClient>>?> = ordreMissionDAO.all

    override fun deleteAll() = ordreMissionDAO.deleteAll()

    }
