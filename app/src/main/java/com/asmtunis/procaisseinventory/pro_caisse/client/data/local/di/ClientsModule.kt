package com.asmtunis.procaisseinventory.pro_caisse.client.data.local.di

import com.asmtunis.procaisseinventory.core.local_storage.localdb.database.ProCaisseDataBase
import com.asmtunis.procaisseinventory.pro_caisse.client.data.local.dao.ClientDAO
import com.asmtunis.procaisseinventory.pro_caisse.client.data.local.repository.ClientRoomRepository
import com.asmtunis.procaisseinventory.pro_caisse.client.data.local.repository.ClientsRoomRepositoryImpl
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import javax.inject.Named
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
class ClientsModule {

    @Provides
    @Singleton
    fun provideClientsDao(
        clientsProCaisseDataBase: ProCaisseDataBase
    ) = clientsProCaisseDataBase.clientDAO()

    @Provides
    @<PERSON>ton
    @Named("Clients")
    fun provideClientsRepository(
        clientDAO: ClientDAO
    ): ClientRoomRepository = ClientsRoomRepositoryImpl(clientDAO = clientDAO)


}