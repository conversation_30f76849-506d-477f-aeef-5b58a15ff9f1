package com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.local.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import com.asmtunis.procaisseinventory.core.local_storage.localdb.core.ProCaisseConstants
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.BonCommande
import com.asmtunis.procaisseinventory.pro_caisse.bon_commande.data.domaine.LigneBonCommande
import com.asmtunis.procaisseinventory.pro_caisse.inventaire.data.domaine.LigneBonCommandeWithImageList
import kotlinx.coroutines.flow.Flow


@Dao
interface InventaireDAO {
    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                " WHERE DEV_Etat LIKE :devEtat and DEV_Station LIKE :station"
    )
    fun getAll(station: String, devEtat: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num= :codeM "
      )
    fun getByCodeM(codeM: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                " WHERE DEV_Etat LIKE :devEtat and DEV_info3 Like :typeInv"+
                " and ${ProCaisseConstants.BON_COMMANDE_TABLE}.isSync=0 and  (${ProCaisseConstants.BON_COMMANDE_TABLE}.Status='INSERTED')"
    )
    fun notSynced(typeInv : String, devEtat: String) : Flow<Map<BonCommande, List<LigneBonCommandeWithImageList>>>



    @Query("SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_Station =:station and DEV_Etat =:BCType order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    fun getByStationMutable(station: String, BCType: String): Flow<List<BonCommande>>

    @Query("SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_Station =:station and (DEV_Etat =:bcType or DEV_Etat =:info) and DEV_info3 =:devinfo3 order by strftime('%Y-%m-%d %H-%M',DEV_Date) desc")
    fun getPatByStationAndTypeMutable(
        station: String,
        bcType: String,
        info: String,
        devinfo3: String
    ): Flow<List<BonCommande>>

    @Query("UPDATE ${ProCaisseConstants.BON_COMMANDE_TABLE} SET isSync = 1, Status= 'SELECTED', DEV_Num = :devNum where DEV_Code_M = :devNumM and isSync = 0")
    fun setSynced(devNum : String, devNumM: String)


    @Query("UPDATE ${ProCaisseConstants.BON_COMMANDE_TABLE} SET Status= 'INSERTED' where DEV_Code_M = :devNumM")
    fun setToInserted(devNumM: String)


    @Query("UPDATE ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} SET isSync = 1, Status= 'SELECTED', LG_DEV_NumBon = :devNum where LG_DEV_NumBon = :devNumM and isSync = 0")
    fun setLgSynced(devNum : String, devNumM: String)

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                "where ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumSerie =:code  " //+
             //   "order by strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_DDm) desc " //+
               // "LIMIT 1"//LIMIT 1 to get only one ligne by num serie to prevent imge not duplicated
    )
    fun getByNumSerie(code: String): Flow<Map<BonCommande, List<LigneBonCommande>>?>

    //List<LigneBonCommande> getByNumSerie(String code);
    @Transaction
    @Query("SELECT * FROM ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} where LG_DEV_NumSerie =:code order by strftime('%Y-%m-%d %H-%M-%S',LG_DEV_DDm) desc")
    fun getByNumSerieList(code: String): Flow<List<LigneBonCommande>>

    @Transaction
    @Query("SELECT * FROM ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} where LG_DEV_NumSerie =:code")
    fun getByNumSerieandCodeClient(code: String): Flow<LigneBonCommande>

    @Query("UPDATE ${ProCaisseConstants.BON_COMMANDE_TABLE} SET DEV_CodeClient = :code_client where DEV_CodeClient = :oldCodeClient")
    fun updateCodeClient(code_client: String, oldCodeClient: String)

    @Query("SELECT ifnull(MAX(cast(substr(DEV_Num,length(:prefix) + 1 ,length('DEV_Num'))as integer)),0)+1 FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} WHERE substr(DEV_Num, 0 ,length(:prefix)+1) = :prefix")
    fun getNewCode(prefix: String): Flow<String>

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insert(item: BonCommande)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertAll(items: List<BonCommande>)





    @Query("DELETE FROM ${ProCaisseConstants.BON_COMMANDE_TABLE}")
    fun deleteAll()

    @Query("DELETE FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_Code_M= :codeM/* and BON_LIV_Exerc=:exercie*/")
    fun deleteByIdM(codeM: String /*,String exercie*/)

    @Query("SELECT *  FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_Code_M= :codeCommande and BON_LIV_Exerc=:exercie")
    fun getListByCodeM(codeCommande: String, exercie: String): Flow<BonCommande>

    @Query("SELECT *  FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_Num= :codeCommande and BON_LIV_Exerc=:exercie")
    fun getByCode(codeCommande: String, exercie: String): Flow<BonCommande>

    @Query("SELECT *  FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_CodeClient= :codeCommande and DEV_info3=:devinf3")
    fun getByCodeClientandPatEtat(codeCommande: String, devinf3: String): Flow<List<BonCommande>>

    @Query("SELECT *  FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} where DEV_CodeClient= :codeCommande and DEV_info3=:devinf3")
    fun getByCodeClientandPatEtatLiveData(
        codeCommande: String,
        devinf3: String
    ): Flow<List<BonCommande>>





    @Query("UPDATE ${ProCaisseConstants.BON_COMMANDE_TABLE} SET DEV_Observation = :devObservation where DEV_Num =:devNum  and BON_LIV_Exerc=:exercie")
    fun updateObservation(devObservation: String, devNum: String, exercie: String)




    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_CodeClient  LIKE '%' || :searchString || '%' or  ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Client_Name  LIKE '%' || :searchString || '%'" +
                "AND DEV_Etat LIKE :devEtat  and DEV_Station LIKE :station " +
                "AND (:nbrMonth = 'Tous' OR (strftime('%m', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :nbrMonth " +
                "AND strftime('%Y', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :year)) " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END DESC "
    )
    fun filterByClient(station: String, searchString: String, sortBy: String, isAsc: Int, devEtat: String, nbrMonth: String, year: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num LIKE '%' || :searchString || '%' " +
                "AND DEV_Etat LIKE :devEtat and DEV_Station LIKE :station " +
                "AND (:nbrMonth = 'Tous' OR (strftime('%m', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :nbrMonth " +
                "AND strftime('%Y', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :year)) " +

                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END DESC "
    )
    fun filterByBonCommandeNum(station: String,searchString: String,  sortBy: String, isAsc: Int, devEtat: String, nbrMonth: String, year: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

    @Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon" +
                " WHERE ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumSerie LIKE '%' || :searchString || '%'  " +
                "AND DEV_Etat LIKE :devEtat and DEV_Station LIKE :station " +
                "AND (:nbrMonth = 'Tous' OR (strftime('%m', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :nbrMonth " +
                "AND strftime('%Y', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :year)) " +
                " ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END DESC "
    )
    fun filterByNumSerie(station: String, searchString: String, sortBy: String, isAsc: Int, devEtat: String, nbrMonth: String, year: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

@Transaction
    @Query(
        "SELECT * FROM ${ProCaisseConstants.BON_COMMANDE_TABLE} " +
                "JOIN ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE} ON ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Num = ${ProCaisseConstants.LIGNE_BON_COMMANDE_TABLE}.LG_DEV_NumBon " +
                " WHERE DEV_Etat LIKE :devEtat and DEV_Station LIKE :station " +
                "AND (:nbrMonth = 'Tous' OR (strftime('%m', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :nbrMonth " +
                "AND strftime('%Y', ${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) = :year)) " +

                "ORDER BY " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 1 THEN DEV_Num END ASC, " +
                "CASE WHEN :sortBy = 'DEV_Num'  AND :isAsc = 2 THEN DEV_Num END DESC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 1 THEN (CAST (DEV_MntTTC AS REAL)) END ASC, " +
                "CASE WHEN :sortBy = 'DEV_MntTTC'  AND :isAsc = 2 THEN (CAST (DEV_MntTTC AS REAL)) END DESC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 1 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END ASC, " +
                "CASE WHEN :sortBy = 'DDmM'  AND :isAsc = 2 THEN strftime('%Y-%m-%d %H-%M-%S',${ProCaisseConstants.BON_COMMANDE_TABLE}.DEV_Date) END DESC "

    )
    fun getAllFiltred(station: String,isAsc: Int,  sortBy: String, devEtat: String, nbrMonth: String, year: String): Flow<Map<BonCommande, List<LigneBonCommande>>>

}
