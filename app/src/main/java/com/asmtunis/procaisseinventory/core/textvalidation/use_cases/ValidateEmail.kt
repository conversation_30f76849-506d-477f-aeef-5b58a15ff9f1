package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import android.util.Patterns
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult

class ValidateEmail {

    fun execute(email: String, required: Boolean = false) : ValidationResult {
        if(email.isBlank() && required) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.email_cant_be_blank)
            )
        }
        if(!Patterns.EMAIL_ADDRESS.matcher(email).matches() && email.isNotBlank()) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.not_a_valid_email)
            )
        }
        return ValidationResult(
            successful = true
        )
    }
}