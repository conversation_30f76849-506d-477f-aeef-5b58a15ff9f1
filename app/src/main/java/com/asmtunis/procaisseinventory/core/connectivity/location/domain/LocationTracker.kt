package com.asmtunis.procaisseinventory.core.connectivity.location.domain

import android.location.Location
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.connectivity.location.model.Adresse

interface LocationTracker {
    suspend fun getCurrentLocation(): Pair<Location?, UiText?>
    suspend fun getCurrentAdress(latitude : Double, longitude : Double):  Pair<Adresse?, UiText?>
}