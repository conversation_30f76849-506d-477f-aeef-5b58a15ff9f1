package com.asmtunis.procaisseinventory.core.utils

import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.pro_caisse.updates.MobilityyUpdateDataType
import com.asmtunis.procaisseinventory.pro_inventory.update.InventoryUpdateDataType
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

object UpdateLoadingStateUtils {
    fun isLoadingInventoryData(getProInventoryDataViewModel: GetProInventoryDataViewModel): Boolean =
        getProInventoryDataViewModel.stationState.loading ||
                getProInventoryDataViewModel.typePrixUnitaireHTState.loading ||
                getProInventoryDataViewModel.tvaState.loading ||
                getProInventoryDataViewModel.fournisseurState.loading ||
                getProInventoryDataViewModel.bonEntreeState.loading ||
                getProInventoryDataViewModel.ligneBonEntreeState.loading ||
                getProInventoryDataViewModel.bonLivraisonState.loading ||
                getProInventoryDataViewModel.ligneBonLivraisonState.loading ||
                getProInventoryDataViewModel.inventaireState.loading ||
                getProInventoryDataViewModel.ligneInventaireState.loading




    fun isLoadingSharedData(
        getSharedDataViewModel: GetSharedDataViewModel
    ): Boolean =
            getSharedDataViewModel.uniteState.loading ||
            getSharedDataViewModel.articleCountState.loading ||
            getSharedDataViewModel.stationStockArticleState.loading ||
            getSharedDataViewModel.articlesState.loading ||
            getSharedDataViewModel.articlesCodeBarePaginationState.loading ||
            getSharedDataViewModel.uniteArticlesPaginationState.loading ||
            getSharedDataViewModel.timbreState.loading

    fun isLoadingCommenSharedData(
        getSharedDataViewModel: GetSharedDataViewModel
    ): Boolean =
            getSharedDataViewModel.deviseState.loading ||
            getSharedDataViewModel.familleState.loading ||
            getSharedDataViewModel.marqueState.loading ||
            getSharedDataViewModel.prefixState.loading||
            getSharedDataViewModel.exericeState.loading||
            getSharedDataViewModel.stationState.loading ||
            getSharedDataViewModel.parametragesState.loading


    fun isLoadingProCaisseData(
        getProCaisseDataViewModel: GetProCaisseDataViewModel

    ): Boolean =
                getProCaisseDataViewModel.clientsState.loading ||
                getProCaisseDataViewModel.immobilisationState.loading ||
                getProCaisseDataViewModel.typeMouvementState.loading ||
                getProCaisseDataViewModel.batimentByUserState.loading ||
                getProCaisseDataViewModel.deplacementOutByUserResponseState.loading ||
                getProCaisseDataViewModel.inventairePieceJointState.loading ||
                getProCaisseDataViewModel.etablissementState.loading ||
                getProCaisseDataViewModel.villeState.loading ||
                getProCaisseDataViewModel.banqueState.loading ||
                getProCaisseDataViewModel.carteRestoState.loading ||
                getProCaisseDataViewModel.ticketRestoState.loading ||
                getProCaisseDataViewModel.chequeCaisseState.loading ||
                getProCaisseDataViewModel.reglementCaisseState.loading ||
                getProCaisseDataViewModel.bonLivraisonState.loading ||
                getProCaisseDataViewModel.ligneTicketCaisseState.loading ||
                getProCaisseDataViewModel.factureState.loading ||
                getProCaisseDataViewModel.bonCommandeState.loading ||
                getProCaisseDataViewModel.ligneBonCommandeState.loading ||
                getProCaisseDataViewModel.bonRetourState.loading ||
                getProCaisseDataViewModel.ligneBonRetourState.loading ||
                getProCaisseDataViewModel.maxNumTicketState.loading ||
                getProCaisseDataViewModel.pricePerStationState.loading ||
                getProCaisseDataViewModel.sessionCaisseState.loading ||
                getProCaisseDataViewModel.sessionCaisseByUserState.loading ||
                getProCaisseDataViewModel.superficieState.loading ||
                getProCaisseDataViewModel.typePointVenteState.loading ||
                getProCaisseDataViewModel.typeServiceState.loading ||
                getProCaisseDataViewModel.familleDnState.loading ||
                getProCaisseDataViewModel.visitesState.loading ||
                getProCaisseDataViewModel.lignesVisitesState.loading ||
                getProCaisseDataViewModel.listConcurrentState.loading ||
                getProCaisseDataViewModel.statisticsState.loading ||
                getProCaisseDataViewModel.newProductState.loading ||
                getProCaisseDataViewModel.promoState.loading ||
                getProCaisseDataViewModel.prixState.loading ||
                getProCaisseDataViewModel.autreState.loading ||
                getProCaisseDataViewModel.typeCommunicationState.loading ||
                getProCaisseDataViewModel.imageState.loading ||
                getProCaisseDataViewModel.etatOrdreMissionState.loading ||
                getProCaisseDataViewModel.ordreMissionWithLinesState.loading



    fun isInventoryUpdateLoading(
        item: InventoryUpdateDataType,
        getProInventoryDataViewModel: GetProInventoryDataViewModel,
        getSharedDataViewModel: GetSharedDataViewModel
    ): Boolean = when (item) {
        InventoryUpdateDataType.STATION -> getSharedDataViewModel.stationState.loading
        InventoryUpdateDataType.STATION_STOCK_ARTICLE -> getSharedDataViewModel.stationStockArticleState.loading
        InventoryUpdateDataType.TYPE_PRIX_UNITAIRE_HT -> getProInventoryDataViewModel.typePrixUnitaireHTState.loading
        InventoryUpdateDataType.TVA -> getProInventoryDataViewModel.tvaState.loading
        InventoryUpdateDataType.UNITE -> getSharedDataViewModel.uniteState.loading
        InventoryUpdateDataType.FAMILLE -> getSharedDataViewModel.familleState.loading
        InventoryUpdateDataType.FOURNISSEUR -> getProInventoryDataViewModel.fournisseurState.loading
        InventoryUpdateDataType.MARQUE -> getSharedDataViewModel.marqueState.loading
        InventoryUpdateDataType.BON_ENTREE -> getProInventoryDataViewModel.bonEntreeState.loading
        InventoryUpdateDataType.LIGNE_BON_ENTREE -> getProInventoryDataViewModel.ligneBonEntreeState.loading
        InventoryUpdateDataType.BON_LIVRAISON -> getProInventoryDataViewModel.bonLivraisonState.loading
        InventoryUpdateDataType.LIGNE_BON_LIVRAISON -> getProInventoryDataViewModel.ligneBonLivraisonState.loading
        InventoryUpdateDataType.INVENTAIRE -> getProInventoryDataViewModel.inventaireState.loading || getProInventoryDataViewModel.ligneInventaireState.loading
        InventoryUpdateDataType.PREFIXE -> getSharedDataViewModel.prefixState.loading
        InventoryUpdateDataType.ARTICLES -> getSharedDataViewModel.articlesState.loading || getSharedDataViewModel.articlesPaginationState.loading
        InventoryUpdateDataType.EXERCICE -> getSharedDataViewModel.exericeState.loading
    }




    fun handleInventoryClick(
        item: InventoryUpdateDataType,
        baseConfig: BaseConfig,
        utilisateur: Utilisateur,
        getProInventoryDataViewModel: GetProInventoryDataViewModel,
        getSharedDataViewModel: GetSharedDataViewModel
    ) {
        when (item) {
            InventoryUpdateDataType.STATION -> getSharedDataViewModel.getStations(baseConfig = baseConfig)
            InventoryUpdateDataType.STATION_STOCK_ARTICLE -> getSharedDataViewModel.getstockArticleByStation(baseConfig = baseConfig)
            InventoryUpdateDataType.TYPE_PRIX_UNITAIRE_HT -> getProInventoryDataViewModel.getTypePrixUnitaireHT(baseConfig = baseConfig)
            InventoryUpdateDataType.TVA -> getProInventoryDataViewModel.getTva(baseConfig = baseConfig)
            InventoryUpdateDataType.UNITE -> getSharedDataViewModel.getUnite(baseConfig = baseConfig)
            InventoryUpdateDataType.FAMILLE -> getSharedDataViewModel.getFamille(baseConfig = baseConfig)
            InventoryUpdateDataType.FOURNISSEUR -> getProInventoryDataViewModel.getFournisseur(baseConfig = baseConfig)
            InventoryUpdateDataType.MARQUE -> getSharedDataViewModel.getMarques(baseConfig = baseConfig)
            InventoryUpdateDataType.BON_ENTREE -> getProInventoryDataViewModel.getBonEntree(baseConfig = baseConfig)
            InventoryUpdateDataType.LIGNE_BON_ENTREE -> getProInventoryDataViewModel.getLigneBonEntree(baseConfig = baseConfig)
            InventoryUpdateDataType.BON_LIVRAISON -> getProInventoryDataViewModel.getBonLivraison(baseConfig = baseConfig)
            InventoryUpdateDataType.LIGNE_BON_LIVRAISON -> getProInventoryDataViewModel.getLigneBonLivraison(baseConfig = baseConfig)
            InventoryUpdateDataType.INVENTAIRE -> getProInventoryDataViewModel.getInventaire(baseConfig = baseConfig)
            InventoryUpdateDataType.PREFIXE -> getSharedDataViewModel.getPrefix(baseConfig = baseConfig)
            InventoryUpdateDataType.ARTICLES -> getSharedDataViewModel.getArticl(
                baseConfig = baseConfig,
                utilisateur = utilisateur
            )
            InventoryUpdateDataType.EXERCICE -> getSharedDataViewModel.getExercice(baseConfig = baseConfig)
        }
    }


    fun handleProcaisseClick(
        item: MobilityyUpdateDataType,
        baseConfig: BaseConfig,
        exerciceCode: String,
        utilisateur: Utilisateur,
        mainViewModel: MainViewModel,
        getProCaisseDataViewModel: GetProCaisseDataViewModel,
        getSharedDataViewModel: GetSharedDataViewModel
    ) {
        when (item) {
            MobilityyUpdateDataType.STATION_STOCK_ARTICLE -> {
                getSharedDataViewModel.getstockArticleByStation(baseConfig = baseConfig)
            }
            MobilityyUpdateDataType.STATION -> getSharedDataViewModel.getStations(baseConfig = baseConfig,)
            MobilityyUpdateDataType.PREFIX -> getSharedDataViewModel.getPrefix(baseConfig = baseConfig)
            MobilityyUpdateDataType.ARTICLES -> getSharedDataViewModel.getArticl(
                baseConfig = baseConfig,
                utilisateur = utilisateur
            )
            MobilityyUpdateDataType.EXERCICE -> getSharedDataViewModel.getExercice(baseConfig = baseConfig)
            MobilityyUpdateDataType.TIMBRE -> getSharedDataViewModel.getTimbre(
                baseConfig = baseConfig,
                utilisateur = utilisateur
            )
            MobilityyUpdateDataType.PRICE_PER_STATION -> getProCaisseDataViewModel.getPricePerStation(baseConfig = baseConfig,)
            MobilityyUpdateDataType.PARAMETRAGES -> getSharedDataViewModel.getParametrages(baseConfig = baseConfig)
            MobilityyUpdateDataType.ETABLISSEMENT -> getProCaisseDataViewModel.getEtablissement(baseConfig = baseConfig,)
            MobilityyUpdateDataType.BANQUES -> getProCaisseDataViewModel.getBanques(baseConfig = baseConfig)
            MobilityyUpdateDataType.CARTE_RESTO -> getProCaisseDataViewModel.getCarteResto(baseConfig = baseConfig)
            MobilityyUpdateDataType.DEVISES -> getSharedDataViewModel.getDevises(baseConfig = baseConfig)
            MobilityyUpdateDataType.FAMILLE_DN -> getProCaisseDataViewModel.getFamilleDn(baseConfig = baseConfig,)
            MobilityyUpdateDataType.SUPERFICIE_DN -> getProCaisseDataViewModel.getSuperficieDn(baseConfig = baseConfig,)
            MobilityyUpdateDataType.TYPE_POINT_VENTE_DN -> getProCaisseDataViewModel.getTypePointVenteDn(baseConfig = baseConfig,)
            MobilityyUpdateDataType.TYPE_SERVICES_DN -> getProCaisseDataViewModel.getTypeServicesDn(baseConfig = baseConfig,)
            MobilityyUpdateDataType.VC_IMAGE -> getProCaisseDataViewModel.getVCImage(baseConfig = baseConfig)
            MobilityyUpdateDataType.VC_TYPE_COMMUNICATION -> getProCaisseDataViewModel.getVCTypeCommunication(baseConfig = baseConfig)
            MobilityyUpdateDataType.VC_LISTE_CONCURRENT -> getProCaisseDataViewModel.getVCListeConcurrent(baseConfig = baseConfig)
            MobilityyUpdateDataType.VC_NEW_PRODUCT -> getProCaisseDataViewModel.getVCNewProduct(baseConfig = baseConfig)
            MobilityyUpdateDataType.VC_AUTRE -> getProCaisseDataViewModel.getVCAutre(baseConfig = baseConfig)
            MobilityyUpdateDataType.VC_PRIX -> getProCaisseDataViewModel.getVCPrix(baseConfig = baseConfig)
            MobilityyUpdateDataType.VC_PROMO -> getProCaisseDataViewModel.getVCPromo(baseConfig = baseConfig)
            MobilityyUpdateDataType.CLIENT -> getProCaisseDataViewModel.getClient(
                baseConfig = baseConfig,
                utilisateur = utilisateur,
                exerciceCode = exerciceCode
            )
            MobilityyUpdateDataType.SESSION_CAISSES -> getProCaisseDataViewModel.getSessionCaisses(
                baseConfig = baseConfig,
                utilisateur = utilisateur,
                clientList = mainViewModel.clientList,
                exerciceCode = exerciceCode
            )
            MobilityyUpdateDataType.SESSION_CAISSES_BY_USER -> getProCaisseDataViewModel.getSessionCaissesByUser(
                baseConfig = baseConfig,
                utilisateur = utilisateur,
                clientList = mainViewModel.clientList,
                exerciceCode = exerciceCode
            )
            MobilityyUpdateDataType.VISITES_DN -> getProCaisseDataViewModel.getVisitesDn(
                baseConfig = baseConfig,
                clientList = mainViewModel.clientList
            )
            MobilityyUpdateDataType.LIGNES_VISITES_DN -> getProCaisseDataViewModel.getLignesVisitesDn(
                baseConfig = baseConfig
            )
            //TODO ADD TOURNE **** ORDRE MISSION
        }
    }

    fun isProcaisseUpdateLoading(
        item: MobilityyUpdateDataType,
        getProCaisseDataViewModel: GetProCaisseDataViewModel,
        getSharedDataViewModel: GetSharedDataViewModel
    ): Boolean = when (item) {
        MobilityyUpdateDataType.TIMBRE -> !getSharedDataViewModel.timbreState.loading
        MobilityyUpdateDataType.PRICE_PER_STATION -> !getProCaisseDataViewModel.pricePerStationState.loading
        MobilityyUpdateDataType.PARAMETRAGES -> !getSharedDataViewModel.parametragesState.loading
        MobilityyUpdateDataType.ETABLISSEMENT -> !getProCaisseDataViewModel.etablissementState.loading
        MobilityyUpdateDataType.BANQUES -> !getProCaisseDataViewModel.banqueState.loading
        MobilityyUpdateDataType.CARTE_RESTO -> !getProCaisseDataViewModel.carteRestoState.loading
        MobilityyUpdateDataType.DEVISES -> !getSharedDataViewModel.deviseState.loading
        MobilityyUpdateDataType.FAMILLE_DN -> !getProCaisseDataViewModel.familleDnState.loading
        MobilityyUpdateDataType.SUPERFICIE_DN -> !getProCaisseDataViewModel.superficieState.loading
        MobilityyUpdateDataType.TYPE_POINT_VENTE_DN -> !getProCaisseDataViewModel.typePointVenteState.loading
        MobilityyUpdateDataType.TYPE_SERVICES_DN -> !getProCaisseDataViewModel.typeServiceState.loading
        MobilityyUpdateDataType.VC_IMAGE -> !getProCaisseDataViewModel.imageState.loading
        MobilityyUpdateDataType.VC_TYPE_COMMUNICATION -> !getProCaisseDataViewModel.typeCommunicationState.loading
        MobilityyUpdateDataType.VC_LISTE_CONCURRENT -> !getProCaisseDataViewModel.listConcurrentState.loading
        MobilityyUpdateDataType.VC_NEW_PRODUCT -> !getProCaisseDataViewModel.newProductState.loading
        MobilityyUpdateDataType.VC_AUTRE -> !getProCaisseDataViewModel.autreState.loading
        MobilityyUpdateDataType.VC_PRIX -> !getProCaisseDataViewModel.prixState.loading
        MobilityyUpdateDataType.VC_PROMO -> !getProCaisseDataViewModel.promoState.loading
        MobilityyUpdateDataType.CLIENT -> !getProCaisseDataViewModel.clientsState.loading
        MobilityyUpdateDataType.SESSION_CAISSES -> !getProCaisseDataViewModel.sessionCaisseState.loading
        MobilityyUpdateDataType.SESSION_CAISSES_BY_USER -> !getProCaisseDataViewModel.sessionCaisseByUserState.loading
        MobilityyUpdateDataType.VISITES_DN -> !getProCaisseDataViewModel.visitesState.loading
        MobilityyUpdateDataType.LIGNES_VISITES_DN -> !getProCaisseDataViewModel.lignesVisitesState.loading
        //shared data
        MobilityyUpdateDataType.STATION -> !getSharedDataViewModel.stationState.loading
        MobilityyUpdateDataType.PREFIX -> !getSharedDataViewModel.prefixState.loading
        MobilityyUpdateDataType.EXERCICE -> !getSharedDataViewModel.exericeState.loading
        MobilityyUpdateDataType.ARTICLES -> !getSharedDataViewModel.articlesState.loading
        MobilityyUpdateDataType.STATION_STOCK_ARTICLE -> !getSharedDataViewModel.stationStockArticleState.loading
    }
}