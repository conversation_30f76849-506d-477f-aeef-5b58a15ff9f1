package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.di

import android.content.Context
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.data.AndroidBluetoothController
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothController
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    @Provides
    @Singleton
    fun provideBluetoothController(@ApplicationContext context: Context): BluetoothController {
        return AndroidBluetoothController(context)
    }
}