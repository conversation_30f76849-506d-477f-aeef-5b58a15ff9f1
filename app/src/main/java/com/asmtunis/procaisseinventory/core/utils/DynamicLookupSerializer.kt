package com.asmtunis.procaisseinventory.core.utils

import kotlinx.serialization.ContextualSerializer
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.InternalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.builtins.PairSerializer
import kotlinx.serialization.builtins.serializer
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encodeToString
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.serializer

@ExperimentalSerializationApi
class DynamicLookupSerializer: KSerializer<Any> {
    override val descriptor: SerialDescriptor = ContextualSerializer(Any::class, null, emptyArray()).descriptor

    @OptIn(InternalSerializationApi::class)
    override fun serialize(encoder: Encoder, value: Any) {
        val actualSerializer = encoder.serializersModule.getContextual(value::class) ?: value::class.serializer()
        encoder.encodeSerializableValue(actualSerializer as KSerializer<Any>, value)
    }

    override fun deserialize(decoder: Decoder): Any {
        return try {
            PairSerializer(Int.serializer(), Int.serializer()).deserialize(decoder)
        } catch (e: Throwable) {
            try {
                decoder.decodeInt()
            } catch (e: Throwable) {
                decoder.decodeString()
            }
        }
    }
}

@OptIn(ExperimentalSerializationApi::class)
val module = SerializersModule {
    contextual(Any::class, DynamicLookupSerializer())
    contextual(Pair::class) {
        PairSerializer(Int.serializer(), Int.serializer())
    }
}
val format = Json { serializersModule = module }
val mm = mapOf<String, Any>()
    .plus("int-int pair" to (5 to 10))
    .plus("int" to 6)
    .plus("string" to "some string")
    .plus("another-int" to 86248726)
    .plus("another-pair" to (56 to 961))
val jsoned = format.encodeToString(mm)
//println(jsoned)
val mmDecoded = format.decodeFromString<Map<String, Any>>(jsoned)
//require(mm==mmDecoded)