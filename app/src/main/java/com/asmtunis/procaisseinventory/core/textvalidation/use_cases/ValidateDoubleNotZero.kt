package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult

class ValidateDoubleNotZero {
    fun execute(value: Double): ValidationResult {
        if (value==0.0) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.cant_be_zero)
            )
        }
        return ValidationResult(
            successful = true
        )
    }
}