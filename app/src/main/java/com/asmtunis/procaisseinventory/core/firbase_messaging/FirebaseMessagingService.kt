package com.asmtunis.procaisseinventory.core.firbase_messaging

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.os.Build
import android.util.Log
import androidx.core.app.NotificationCompat
import com.asmtunis.procaisseinventory.MainActivity
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.Globals
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import kotlin.random.Random


class FirebaseMessagingService : FirebaseMessagingService() {
    private val random = Random

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
       remoteMessage.notification?.let { message -> sendNotification(message) }
    }

    private fun sendNotification(message: RemoteMessage.Notification) {
        // If you want the notifications to appear when your app is in foreground
        val intent = Intent(this, MainActivity::class.java).apply {
            //addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)

            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            addFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            putExtra(Globals.FROM_NOTIFICATION, true)


        }

        Log.d("dgrfsffdgs", "mm "+ message.notificationCount)

        val pendingIntent = PendingIntent.getActivity(this, random.nextInt(), intent, PendingIntent.FLAG_IMMUTABLE)
        //val pendingIntent = PendingIntent.getActivity(this, random.nextInt(), intent, PendingIntent.FLAG_CANCEL_CURRENT or PendingIntent.FLAG_IMMUTABLE)
       //  val pendingIntent = PendingIntent.getActivity(this, random.nextInt(), intent, PendingIntent.FLAG_UPDATE_CURRENT)




        val channelId = this.getString(R.string.default_notification_channel_id)

        val notificationBuilder = NotificationCompat.Builder(this, channelId)
            .setContentTitle(message.title)
            .setContentText(message.body)
            .setSmallIcon(R.mipmap.ic_launcher)
            .setAutoCancel(true)
             .setContentIntent(pendingIntent)
            .setVibrate(longArrayOf(500, 1000, 500, 1000))
            //.setVibrate(message.vibrateTimings)
            .setDefaults(Notification.DEFAULT_SOUND)
            .setPriority(NotificationCompat.PRIORITY_MAX)

        val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(channelId, CHANNEL_NAME, NotificationManager.IMPORTANCE_HIGH).apply {
                enableLights(true)
            enableVibration(true)
            }
            manager.createNotificationChannel(channel)
        }

        manager.notify(random.nextInt(), notificationBuilder.build())
    }



   /* override fun onDestroy() {
        mDispatcher.onServicePreSuperOnDestroy()
        super.onDestroy()
    }*/
     override fun onNewToken(token: String) {
        // If you want to send messages to this application instance or
        // manage this apps subscriptions on the server side, send the
        // FCM registration token to your app server.
   //     Log.d("FCMxxx","New token: $token")
     //dataViewModel.saveFirebaseToken(token)


    }

    companion object {
        const val CHANNEL_NAME = "FCM notification channel"
    }


}