package com.asmtunis.procaisseinventory.core.ktor

import android.util.Log
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.network_errors.domaine.NetworkError
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.network.sockets.SocketTimeoutException
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.RedirectResponseException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.request.delete
import io.ktor.client.request.forms.MultiPartFormDataContent
import io.ktor.client.request.forms.formData
import io.ktor.client.request.header
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.request.url
import io.ktor.http.isSuccess
import io.ktor.utils.io.InternalAPI
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.flow
import kotlinx.serialization.SerializationException
import java.io.IOException
import java.net.ConnectException
import java.net.UnknownHostException
import kotlin.coroutines.coroutineContext


@OptIn(InternalAPI::class)
suspend inline fun <reified T> executePostApiCall(
    client: HttpClient,
    baseUrl: String = Urls.BASE_URL,
    endpoint: String,
    queryParams: Map<String, Any> = mapOf(),
    headers: Map<String, String> = emptyMap(),
    formsData: Map<String, String> = emptyMap(),
    baseConfig: String = "",
    customError: String? = null
): Flow<DataResult<T>> = flow {
   //  try {
        emit(DataResult.Loading())
        val response = client.post {


               if(Globals.USER_ID.isNotEmpty())
                header("user", Globals.USER_ID)


              if(headers.isNotEmpty())
                headers.forEach { (key, value) -> header(key, value) }

            url("$baseUrl$endpoint")

            if(formsData.isNotEmpty()) {
                setBody(
                    MultiPartFormDataContent(
                        formData {
                            formsData.forEach { (key, value) -> append(key, value) }
                        }
                    )
                )
            }



         if(queryParams.isNotEmpty()) {
             queryParams.forEach { (key, value) -> parameter(key, value) }
         }

         /*   url {
                queryParams.forEach { (key, value) -> parameters.append(key, value) }
            }*/

            if(baseConfig.isNotEmpty()) {
                body = baseConfig
            }

        }

        if (response.status.isSuccess()) {
            emit(DataResult.Success(response.body<T>()))
        } else {
            emit(DataResult.Error(message = "${response.status.value}: ${response.status.description}"))
        }
//    } catch (e: Exception) {
//        coroutineContext.ensureActive() // to check if the coroutine is still active
//        val errorMessage = handleExeptions(e = e, customError = customError)
//        emit(DataResult.Error(errorMessage))
//    }
}.catch {
  //  emit(DataResult.Error(maskUrls(input = it.message, customError = customError?: "error")))
    emit(DataResult.Error(it.message?: "Error"))
}

@OptIn(InternalAPI::class)
suspend inline fun <reified T> executeDeleteApiCall(
    client: HttpClient,
    baseUrl: String = Urls.BASE_URL,
    endpoint: String,
    queryParams: Map<String, Any> = mapOf(),
    headers: Map<String, String> = emptyMap(),
    formsData: Map<String, String> = emptyMap(),
    baseConfig: String = "",
    customError: String? = null
): Flow<DataResult<T>> = flow {
     try {
       emit(DataResult.Loading())
        val response = client.delete {


            if(Globals.USER_ID.isNotEmpty())
                header("user", Globals.USER_ID)


            if(headers.isNotEmpty())
                headers.forEach { (key, value) -> header(key, value) }

            url("$baseUrl$endpoint")

            if(formsData.isNotEmpty())
                setBody(
                    MultiPartFormDataContent(
                        formData {
                            formsData.forEach { (key, value) -> append(key, value) }
                        }
                    )
                )


            if(queryParams.isNotEmpty())
                queryParams.forEach { (key, value) -> parameter(key, value) }

            /*   url {
                   queryParams.forEach { (key, value) -> parameters.append(key, value) }
               }*/

            if(baseConfig.isNotEmpty())
                body = baseConfig
        }

        if (response.status.isSuccess()) {
            emit(DataResult.Success(response.body<T>()))
        } else {
            emit(DataResult.Error(message = "${response.status.value}: ${response.status.description}"))
        }
    } catch (e: Exception) {
         coroutineContext.ensureActive() // to check if the coroutine is still active
        val errorMessage = handleExeptions(e = e, customError = customError)
        emit(DataResult.Error(errorMessage))
    }
}



//TODO make customError: String of type uitext (for custom localisation)
fun handleExeptions(e: Exception, customError: String?): String = when (e) {

    is ConnectException -> "Échec de connexion. Veuillez réessayer plus tard."
    is ClientRequestException -> "HTTP Error: ${e.response.status}" //4xx client errors
    is ServerResponseException -> "Erreur serveur: ${e.response.status}" // 5xx
    is RedirectResponseException -> "${e.response.status}: Not following redirection to ${ e.response.headers["Location"]}" //3xx redirects
    is SocketTimeoutException -> "Time out: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    is UnknownHostException -> "Unknown Host Exception: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    is IOException -> "IO Exception: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    is SerializationException -> "Serialization Exception: " + (e.cause?.localizedMessage ?: e.localizedMessage ?: "Erreur inconnue")
    else -> customError ?: e.cause?.localizedMessage?: e.localizedMessage?: "Erreur inconnue"
}
/*
suspend fun <T> apiCall(customError: String? = null, apiCall: suspend () -> T): Flow<DataResult<T>> = flow {
    try {
        emit(DataResult.Loading())
        val result = apiCall()
        emit(DataResult.Success(result))
    }catch (e: Exception) {
        emit(DataResult.Error(customError?: e.localizedMessage ?: e.message?: "Erreur inconnue"))
    }

    catch (e: ClientRequestException) {
        emit(DataResult.Error(customError?: "HTTP Error: ${e.response.status}"))
    } catch (e: ConnectException) {
        emit(DataResult.Error(customError?: "Échec de connexion. Veuillez réessayer plus tard."))
    }
}*/


 fun insertNetworkError(
    proCaisseLocalDb: ProCaisseLocalDb,
    url: String,
    extraInfo: String = "",
    errorMessage: String?
) {
    val networkError = NetworkError(
        url = url,
        extraInfo = extraInfo,
        errorMessage = errorMessage
    )
    proCaisseLocalDb.networkErrors.insert(item = networkError)
}


fun veryLongLog(tag: String, message: String) {
    val maxLength = 4000 // Maximum length of each part

    if (message.length <= maxLength) {
        Log.v(tag, message)
    } else {
        var startIndex = 0
        var endIndex = maxLength

        while (startIndex < message.length) {
            // Ensure endIndex does not exceed the message length
            if (endIndex > message.length) {
                endIndex = message.length
            }

            val part = message.substring(startIndex, endIndex)
            Log.v(tag, part)

            startIndex = endIndex
            endIndex += maxLength
        }
    }



}

fun extractCurrentPage(input: String): Int? {
    val regex = """Current page:\s*(\d+)""".toRegex()
    val matchResult = regex.find(input)
    return matchResult?.groups?.get(1)?.value?.toInt()
}


fun maskUrls(input: String?, customError: String): String {
    // Regex to match URLs
    val urlRegex = """(https?://[^\s]+)""".toRegex()

    // Replace each URL with asterisks of the same length
    return urlRegex.replace(input?: customError) { matchResult ->
        "*".repeat(matchResult.value.length)
    }
}