package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult

class ValidatePassword {

    fun execute(password: String) : ValidationResult {
        if(password.isEmpty()) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.password_requiere_one_charachter)
            )
        }
     /*   if(password.length < 8) {
            return ValidationResult(
                successful = false,
                errorMessage = "The password needs to consist of at least 8 characters"
            )
        }*/
       /* val containsLetterAndDigits = password.any { it.isLetter() } && password.any { it.isDigit() }

        if (!containsLetterAndDigits) {
            return ValidationResult(
                successful = false,
                errorMessage = "The password needs to contain at least one letter and digit"
            )
        }*/
        return ValidationResult(
            successful = true
        )
    }
}