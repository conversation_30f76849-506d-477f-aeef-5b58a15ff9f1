@file:OptIn(SavedStateHandleSaveableApi::class)

package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.SavedStateHandleSaveableApi
import androidx.lifecycle.viewmodel.compose.saveable
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothController
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothDeviceDomain
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.ConnectionResult
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class BluetoothViewModel @Inject constructor(
    private val bluetoothController: BluetoothController,
    savedStateHandle : SavedStateHandle
): ViewModel() {


    var isBluetoothEnabled  by savedStateHandle.saveable { mutableStateOf(false) }
        private set
    fun onIsBluetoothEnabledChange(state: Boolean) {
        isBluetoothEnabled = state
    }

    var bluetoothAdapter: BluetoothAdapter?  by  mutableStateOf(null)
        private set
    fun onBluetoothAdapterChange(state: BluetoothAdapter?) {
        bluetoothAdapter = state
    }

    var bluetoothManager: BluetoothManager?  by  mutableStateOf(null)
        private set
    fun onBluetoothManagerChange(state: BluetoothManager?) {
        bluetoothManager = state
    }


    private val _state = MutableStateFlow(BluetoothUiState())
    val state = combine(
        bluetoothController.scannedDevices,
        bluetoothController.pairedDevices,
        bluetoothController.isScanning,
        bluetoothController.isConnecting,
        _state
    ) { scannedDevices, pairedDevices , isScanning, isConnecting, state ->
        state.copy(
            scannedDevices = scannedDevices,
            pairedDevices = pairedDevices,
            isScanning = isScanning,
            isConnecting = isConnecting
        )
    }.stateIn(viewModelScope, SharingStarted.WhileSubscribed(6000), _state.value)

    private var deviceConnectionJob: Job? = null

    init {
        bluetoothController.isConnected.onEach { isConnected ->
            _state.update { it.copy(isConnected = isConnected) }
        }.launchIn(viewModelScope)

        bluetoothController.errors.onEach { error ->
            _state.update { it.copy(
                errorMessage = error
            ) }
        }.launchIn(viewModelScope)
    }

    fun connectToDevice(device: BluetoothDeviceDomain) {
        _state.update { it.copy(isConnecting = true) }
        deviceConnectionJob = bluetoothController
            .connectToDevice(device)
            .listen()
    }

    fun disconnectFromDevice() {
        deviceConnectionJob?.cancel()
        bluetoothController.closeConnection()
        _state.update { it.copy(
            isConnecting = false,
            isConnected = false
        ) }
    }



    fun startScan() {
       // _state.value = BluetoothUiState()
        bluetoothController.startDiscovery()

        viewModelScope.launch {

            delay(5000)
            stopScan()
        }
    }

    fun stopScan() {
        bluetoothController.stopDiscovery()
    }

    private fun Flow<ConnectionResult>.listen(): Job {
        return onEach { result ->
            when(result) {
                ConnectionResult.ConnectionEstablished -> {
                    _state.update { it.copy(
                        isConnected = true,
                        isConnecting = false,
                        errorMessage = null
                    ) }
                }
                is ConnectionResult.Error -> {
                    _state.update { it.copy(
                        isConnected = false,
                        isConnecting = false,
                        errorMessage = result.message
                    ) }
                }
            }
        }
            .catch { throwable ->
                bluetoothController.closeConnection()
                _state.update { it.copy(
                    isConnected = false,
                    isConnecting = false,
                    errorMessage = throwable.cause?.message?: throwable.localizedMessage
                ) }
            }
            .launchIn(viewModelScope)
    }

    override fun onCleared() {
        super.onCleared()
        bluetoothController.release()
    }
}