package com.asmtunis.procaisseinventory.core.utils

import android.util.Log
import androidx.room.TypeConverter
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.auth.spalsh_screen.data.activationservice.domaine.Licence
import com.asmtunis.procaisseinventory.pro_caisse.client.data.domaine.Client
import com.asmtunis.procaisseinventory.pro_caisse.distribution_numerique.data.domaine.LigneVisitesDn
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.Collections
import kotlin.reflect.KClass

fun <T> checkTypeUsingReflection(data: T?) {
    if (data != null) {
        val type: KClass<out Any> = data!!::class

        Log.d("fdssfsfdsffdeff", "type.simpleName "+ type.simpleName)
    } else {
        Log.d("fdssfsfdsffdeff","data is null")
    }
}
class AuthorizationTypeConverter {

    @TypeConverter
    //fun stringToList(data: String?): List<List<Authorization>> {
    fun stringToList(data: String?): List<Authorization> {
        if (data == null) {
            return Collections.emptyList()
        }


       // val baseconfig: Authorization = Json.decodeFromString(data)

      //  return   listOf(Json.decodeFromString(data))
        return   Json.decodeFromString(data)
    }

    @TypeConverter
    //fun listToString(someObjects: List<List<Authorization>>): String {
    fun listToString(someObjects: List<Authorization>): String {
        //return Json.encodeToString(someObjects.flatten())
        return Json.encodeToString(someObjects)
    }

}


class LigneVisiteTypeConverter {

    @TypeConverter
    fun stringToList(data: String?): List<LigneVisitesDn> {
        if (data == null) {
            return Collections.emptyList()
        }


        // val baseconfig: Authorization = Json.decodeFromString(data)

        return   Json.decodeFromString(data)
    }

    @TypeConverter
    fun listToString(someObjects: List<LigneVisitesDn>): String {
        return Json.encodeToString(someObjects)
    }

}


class StatisticsTypeConverter {

    @TypeConverter
    fun stringToList(data: String?): List<Client> {
        if (data == null) {
            return Collections.emptyList()
        }


        // val baseconfig: Authorization = Json.decodeFromString(data)

        return   Json.decodeFromString(data)
    }

    @TypeConverter
    fun listToString(someObjects: List<Client>): String {
        return Json.encodeToString(someObjects)
    }

}


class StringTypeConverter {

    @TypeConverter
    fun stringToList(data: String?): List<String> {
        if (data == null) {
            return Collections.emptyList()
        }


        // val baseconfig: Authorization = Json.decodeFromString(data)

        return   Json.decodeFromString(data)
    }

    @TypeConverter
    fun listToString(someObjects: List<String>): String {
        return Json.encodeToString(someObjects)
    }

}





class LicenceTypeConverter {

    @TypeConverter
    fun stringToList(data: String?): List<Licence> {
        if (data == null) {
            return Collections.emptyList()
        }


        // val baseconfig: Authorization = Json.decodeFromString(data)

        return   Json.decodeFromString(data)
    }

    @TypeConverter
    fun listToString(someObjects: List<Licence>): String {
        return Json.encodeToString(someObjects)
    }

}


