package com.asmtunis.procaisseinventory.core.local_storage.localdb.core

class ProCaisseConstants {


    companion object {
        //Room
        const val PRO_CAISSE_DB_NAME = "procaisse_mobility"
        const val BASECONFIG_TABLE = "Base_Config"
        const val PARAMETRAGE_TABLE = "Prarametrage"
        const val UTILISATEUR_TABLE = "Utilisateur"
        const val AUTHORIZATION_TABLE = "Authorization"
        const val Articles_TABLE = "Articles"

        const val UNITE_Articles_TABLE = "Unite_Articles"

        const val Articles_CODE_BAR_TABLE = "Articles_Code_Bar"
        const val CLIENTS_ARTICLE_PRIX_TABLE = "Clients_Articles_Prix"
        const val ETABLISSEMENT_TABLE = "Etablisement"
        const val BANQUE_TABLE = "Banque"
        const val FACTURE_TABLE = "Facture"

        const val NETWORK_ERRORS = "Network_Errors"

        const val DEVISE_TABLE = "Devise"
        const val CHEQUE_CAISSE_TABLE = "Cheque_Caisse"
        const val TICKET_RESTO_TABLE = "Ticket_Resto"
        const val CARTE_RESTO_TABLE = "Carte_Resto"
        const val STATISTIQUES_TABLE = "Statistiques"
        const val TIMBRE_TABLE = "Timbre"
        const val TIMBRE_VILLE = "Ville"
        const val CLIENT_TABLE = "Client"
        const val SESSION_CAISSE_TABLE = "Session_Caisse"
        const val PREFIX_TABLE = "Prefix"
        const val EXERCICE_TABLE = "Exercice"
        const val PRICE_PERSTATION_TABLE = "Price_perStation"
        const val REGLEMENT_CAISSE_TABLE = "Reglement_Caisse"
        const val TICKET_TABLE = "Ticket"
        const val LIGNE_TICKET_TABLE = "Ticket_Ligne"

        const val BON_COMMANDE_TABLE = "Bon_Commande"
        const val LIGNE_BON_COMMANDE_TABLE = "Bon_Commande_Ligne"


        const val BON_RETOUR_TABLE = "Bon_Retour"
        const val LIGNE_BON_RETOUR_TABLE = "Bon_Retour_Ligne"


        // TOURNEE
        const val ORDRE_MISSION_TABLE = "Ordre_mission"
        const val ORDRE_MISSION_LIGNE_TABLE = "Ordre_mission_ligne"
        const val ETAT_ORDRE_MISSION_TABLE = "Ordre_mission_Etat"

        // DISTRIBUTION NUMERIQUE
        const val VISITE_TABLE = "Dn_Visite"
        const val LIGNE_VISITE_TABLE = "Dn_Ligne_Visite"
        const val TYPE_SERVICE_TABLE = "Dn_Type_Services"
        const val SUPERFICIE_TABLE = "Dn_Superficie"
        const val FAMILLE_TABLE = "Dn_Famille"
        const val TYPE_POINT_VENTE_TABLE = "Dn_Type_point_vente"

        //Veille Concurentiel

        const val LISTE_CONCURRENT_TABLE = "vc_ListeConcurrent"
        const val TYPE_COMMUNICATION_TABLE = "vc_TypeCommunication"
        const val NEW_PRODUCT_TABLE = "vc_NewProduct"
        const val PROMO_TABLE = "vc_Promo"
        const val PRIX_TABLE = "vc_Prix"
        const val AUTRE_TABLE = "vc_Autre"
        const val IMAGE_TABLE = "vc_Image"


        const val LICENCE_TABLE = "Licence"


        //INVENTAIRE
        const val IMMOBILISATION_TABLE = "Immobilisation"
        const val BATIMENT_BY_USER = "Batiments_byUser"
        const val TYPE_MOUVEMENT = "Type_Mouvement"
        const val DEPLACEMENT_OUT_BY_USER = "DeplacementOut_ByUser"



        const val PIECE_JOINTS_TABLE = "Piece_Joints"

    }
}