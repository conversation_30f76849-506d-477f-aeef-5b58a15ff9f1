package com.asmtunis.procaisseinventory.core.local_storage.datastore.preferences.abstraction

import kotlinx.coroutines.flow.Flow

interface DataStoreRepository {

    suspend fun putString(key: String, value: String)
    suspend fun putInt(key: String, value: Int)
    suspend fun putBoolean(key: String, value: Boolean)
     fun getString(key: String, default: String = ""): Flow<String?>
     fun getInt(key: String, default: Int = -0): Flow<Int>
     fun getBoolean(key: String, default: Boolean = false): Flow<Boolean>
}