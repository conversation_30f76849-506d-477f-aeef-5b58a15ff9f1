package com.asmtunis.procaisseinventory.core.sync_workmanager

import android.content.Context
import androidx.hilt.work.HiltWorker
import androidx.work.Data
import androidx.work.Worker
import androidx.work.WorkerParameters
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals.TICKET_FACTURE_DEJA
import com.asmtunis.procaisseinventory.core.connectivity.internet.ListenNetwork
import com.asmtunis.procaisseinventory.core.ktor.Urls.BASE_URL
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.AUTO_FACTURE_AUTHORISATION
import com.asmtunis.procaisseinventory.core.local_storage.datastore.utils.SELECTED_BASE_CONFIG
import com.asmtunis.procaisseinventory.core.model.DataResult
import com.asmtunis.procaisseinventory.core.model.GenericObject
import com.asmtunis.procaisseinventory.core.utils.IoDispatcher
import com.asmtunis.procaisseinventory.core.utils.StringUtils.validateBaseUrl
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketUpdate
import com.asmtunis.procaisseinventory.pro_caisse.bon_livraison.data.domaine.TicketWithFactureAndPayments
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseLocalDb
import com.asmtunis.procaisseinventory.pro_caisse.global_data_class.ProCaisseRemote
import com.simapps.ui_kit.utils.getCurrentDateTime
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.encodeToJsonElement

@HiltWorker
class SyncWorker
    @AssistedInject
    constructor(
        @Assisted context: Context,
        @Assisted workerParams: WorkerParameters,
        private val listenNetwork: ListenNetwork,
        private val proCaisseRemote: ProCaisseRemote,
        private val proCaisseLocalDb: ProCaisseLocalDb,
        @IoDispatcher private val dispatcherIO: CoroutineDispatcher,
    ) : Worker(context, workerParams) {
        var baseConfig: BaseConfig = BaseConfig()

        @OptIn(DelicateCoroutinesApi::class)
        override fun doWork(): Result {
            return try {
                GlobalScope.launch(dispatcherIO) {
                    val baseConf = proCaisseLocalDb.dataStore.getString(SELECTED_BASE_CONFIG).first()

                    if (baseConf.isNullOrEmpty()) return@launch
                    baseConfig = Json.decodeFromString(baseConf)

                    BASE_URL = String.format(validateBaseUrl(baseConfig), baseConfig.adresse_ip, baseConfig.port)
                    getNotSyncBonLivraison()
                }

                Result.success()
            } catch (exception: Exception) {
                exception.printStackTrace()

                Result.failure(Data.Builder().putString("errorWorkerProcaisse", exception.toString()).build())
            }
        }

        private suspend fun syncBonLivraison(ticketsWithLinesAndPaymentsNotSync: List<TicketWithFactureAndPayments>) {
            coroutineScope {
                val autoFacture = proCaisseLocalDb.dataStore.getBoolean(AUTO_FACTURE_AUTHORISATION).first()

                val baseConfigObj =
                    GenericObject(
                        baseConfig,
                        Json.encodeToJsonElement(ticketsWithLinesAndPaymentsNotSync),
                    )

                proCaisseRemote.ticket.addBatchTicketWithLignesTicketAndPayment(
                    baseConfig = Json.encodeToString(baseConfigObj),
                    autoFacture = autoFacture,
                ).onEach {
                        result ->
                    when (result) {
                        is DataResult.Success -> {
                            //   responseAddBatchTicketWithLignesState = AddBatchTicketWithLignesState(data = result.data, loading = false, error = null)
                            for (i in result.data!!.indices) {
                                val ticketUpdate = result.data[i]
                                //   if (!ticketUpdate.code.equals("10200") && !ticketUpdate.code.equals("10201") && !ticketUpdate.code.equals("10304"))
                                // TODO HANDLE OTHER CASES HERE
                                // return@onEach

                                if (ticketUpdate.code == "10201")
                                    {
                                        proCaisseLocalDb.ligneBonLivraison.deleteByCodeM(
                                            code = ticketUpdate.tIKNumTicketM!!,
                                            exercice = ticketUpdate.tIKExerc!!,
                                        )
                                        proCaisseLocalDb.bonLivraison.deleteByCodeM(
                                            code = ticketUpdate.tIKNumTicketM!!,
                                            exercice = ticketUpdate.tIKExerc!!,
                                        )
                                    } else if (ticketUpdate.code == "10200") {
                                    updateLocalData(ticketUpdate = ticketUpdate)
                                }

                                if (ticketUpdate.code != "10200") {
                                    proCaisseLocalDb.bonLivraison.updateSyncErrorMsg(
                                        tikNumTicketM = ticketUpdate.tIKNumTicketM!!,
                                        errorMsg = ticketUpdate.message ?: "UnkownError",
                                    )
                                }
                            }
                        }

                        is DataResult.Loading -> {
                            // responseAddBatchTicketWithLignesState = AddBatchTicketWithLignesState(data = null, loading = true, error = null)
                        }

                        is DataResult.Error -> {
                            //  responseAddBatchTicketWithLignesState = AddBatchTicketWithLignesState(data = null, loading = false, error = result.message)
                        }
                    }
                }.flowOn(dispatcherIO).launchIn(this)
            }
        }

        private suspend fun updateLocalData(ticketUpdate: TicketUpdate) {
            coroutineScope {
                val ddm = getCurrentDateTime()
                proCaisseLocalDb.ligneBonLivraison.updateNumTicket(
                    codeM = ticketUpdate.tIKNumTicketM?: "",
                    newCode = ticketUpdate.tIKNumTicket,
                    exercice = ticketUpdate.tIKExerc?: "",
                    carnet = ticketUpdate.tIKIdCarnet?: "",
                )
                if (ticketUpdate.tIKNumeroBL == null) {
                    // Auto facture == true

                    proCaisseLocalDb.bonLivraison.updateBLNumber(
                        tikNumTicket = ticketUpdate.tIKNumTicket,
                        tikNumTicketM = ticketUpdate.tIKNumTicketM?: "",
                        tikDdm = ddm,
                    )
                } else {
                    // Auto facture == false
                    proCaisseLocalDb.bonLivraison.updateTicketNumber(
                        tikNumBl = ticketUpdate.tIKNumeroBL?: "",
                        tikNumTicket = ticketUpdate.tIKNumTicket,
                        tikNumTicketM = ticketUpdate.tIKNumTicketM?: "",
                        tikDdm = ddm,
                    )
                }
                if (ticketUpdate.observation != null) {
                    if (!ticketUpdate.observation.equals("")) {
                        proCaisseLocalDb.bonCommande.updateObservation(
                            devObservation = ticketUpdate.observation?: "",
                            devNum = ticketUpdate.tIKNumTicketM?: "",
                            exercie = ticketUpdate.tIKExerc?: "",
                        )
                    }
                }
                if (ticketUpdate.message != null) {
                    if (ticketUpdate.message.equals(TICKET_FACTURE_DEJA)) {
                        proCaisseLocalDb.bonCommande.updateObservation(
                            devObservation = ticketUpdate.message?: "",
                            devNum = ticketUpdate.tIKNumTicketM?: "",
                            exercie = ticketUpdate.tIKExerc?: "",
                        )
                    }
                }
                updateClientSold(
                    cLICode = ticketUpdate.codeClient?: "",
                    ticketUpdate = ticketUpdate,
                )
                proCaisseLocalDb.reglementCaisse.updateRegCodeAndState(
                    regCode = ticketUpdate.tIKNumTicketM?: "",
                    regCodeM = ticketUpdate.tIKNumTicketM?: "",
                )
                proCaisseLocalDb.chequeCaisse.updateRegCodeAndState(
                    regCode = ticketUpdate.tIKNumTicketM?: "",
                    regCodeM = ticketUpdate.tIKNumTicketM?: "",
                )
                proCaisseLocalDb.ticketResto.updateRegCodeAndState(
                    regCode = ticketUpdate.tIKNumTicketM?: "",
                    regCodeM = ticketUpdate.tIKNumTicketM?: "",
                )
            }
        }

        private suspend fun updateClientSold(
            cLICode: String,
            ticketUpdate: TicketUpdate,
        )  {
            coroutineScope {
                proCaisseLocalDb.clients.updateMoneyClient(
                    codeClt = cLICode,
                    soldClient = ticketUpdate.soldeClient?: "",
                    cliCredit = ticketUpdate.credit?: "",
                    cliDebit = ticketUpdate.debit?: "",
                )
            }
        }

        private suspend fun getNotSyncBonLivraison() {
            coroutineScope {
              //  listenNetwork.isConnected.distinctUntilChanged().collect { isConnected ->
                   // if (!isConnected) return@collect

                    proCaisseLocalDb.bonLivraison.notSynced().distinctUntilChanged().collectLatest {
                        if (it == null) {
                            return@collectLatest
                        }
                        if (it.isNotEmpty())
                            {
                                //  coroutineScope {
                                syncBonLivraison(
                                    ticketsWithLinesAndPaymentsNotSync = it,
                                )
                                //  }
                            }

                        cancel()
                    }
              //  }
            }
        }
    }
