package com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues

object AuthorizationValuesProCaisse {
    const val ADD_ARTICLE = "800000001"
    const val BC_TO_BL = "102518333"
    const val VEILLE_CONCURENTIELLE = "109585133"
    const val AUTO_FACTURE = "109585111"
    const val CUSTOM_LOCATION = "109585132"
    const val FIXED_DISCOUNT = "507139332"
    const val NO_DISCOUNT = "407139332"
    const val ART_REM_DISCOUNT = "307639332"
    const val FREE_DISCOUNT = "202518251"
    const val MODIFY_CLIENT = "510000000"
    const val BL_TIMBRE = "480000000"
    const val IS_CHAHIA_CLIENT = "530000000"//todo see why (with aymen, <PERSON><PERSON>) it's NOW named scan code bar article by type user
    const val ART_ZERO_STOCK = "410000000"
    const val PATRIMOINE = "102518214"
    const val BATIMENT = "09999999999999999999999999999999999999999999999"
    const val CREATION_TOURNE_AUTO = "102518282"
    const val CLOT_SESSION_AUTO = "102558282"
    const val FILTRE_CLIENT = "106582832"
    const val TOURNE = "106585732"
    const val AJOUT_CLIENT = "106585032"
    const val CREDIT_CLIENT = "107585732"
    const val CHOOSE_PRICE_CATEGERI = "202000000"
    const val BL = "102518251"
    const val BC = "107639332"
    const val BR = "107139332"
    const val REMISE = "107635732"
    const val DEPENSE = "490000000"
    const val REGLEMENT_PARTIEL = "810000000"

    const val MODIFY_PRICE = "540000000"
}
