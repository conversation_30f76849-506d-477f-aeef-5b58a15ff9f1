package com.asmtunis.procaisseinventory.core.connectivity.location

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.asmtunis.procaisseinventory.core.connectivity.location.domain.LocationTracker
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class LocationViewModule @Inject constructor(
    private val locationTracker: LocationTracker
) : ViewModel() {

    var locationState by mutableStateOf(LocationState())
        private set

    init {
        getCurrentLocation()
    }
    fun getCurrentLocation() {
        viewModelScope.launch {
            locationState = locationState.copy(
                isLoading = true,
                error = null
            )
             val result = locationTracker.getCurrentLocation()
            val location = result.first
            val errorMsg = result.second
            if (location != null) {
                getCurrentAdress(
                    latitude = location.latitude,
                    longitude = location.longitude
                )
            }
            else {
                kotlin.run {
                    locationState = locationState.copy(
                        isLoading = false,
                        error = errorMsg
                    )
                }
            }
           /* locationTracker.getCurrentLocation().first?.let { location ->

                getCurrentAdress(
                    latitude = location.latitude,
                    longitude = location.longitude
                )
            } ?: kotlin.run {
                state = state.copy(
                    isLoading = false,
                    error = "Couldn't retrieve location. Make sure to grant permission and enable GPS."
                )
            }*/
        }
    }

    fun getCurrentAdress(
        latitude: Double,
        longitude: Double
    ) {
        viewModelScope.launch {
            val result = locationTracker.getCurrentAdress(latitude, longitude)
            val adress = result.first
            val errorMsg = result.second

            locationState = if (adress != null) {
                locationState.copy(
                    latitude = latitude,
                    longitude = longitude,
                    adresse = adress,
                    isLoading = false
                )
            } else {
                kotlin.run {
                    locationState.copy(
                        isLoading = false,
                        error = errorMsg
                    )
                }
            }
         /*   locationTracker.getCurrentAdress(latitude, longitude)?.let { adress ->


                state = state.copy(
                    latitude = latitude,
                    longitude = longitude,
                    adresse = adress,
                    isLoading = false,
                    error = null
                )
            } ?: kotlin.run {
                state = state.copy(
                    isLoading = false,
                    error = "Couldn't retrieve location. Make sure to grant permission and enable GPS."
                )
            }*/
        }
    }

    fun resetCurrentLocation() {
        locationState = LocationState()
      /*  state.copy(
            isLoading = false,
            error = null,
            location = null,
            adresse = null
        )*/
    }




}
