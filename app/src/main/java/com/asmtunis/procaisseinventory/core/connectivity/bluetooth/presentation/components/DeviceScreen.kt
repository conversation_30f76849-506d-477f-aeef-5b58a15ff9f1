package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.components

import android.annotation.SuppressLint
import android.bluetooth.BluetoothClass
import android.content.res.Configuration
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.BluetoothUtil.getBluetoothDeviceIcon
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothDevice
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.BluetoothUiState
import com.asmtunis.procaisseinventory.shared_ui_components.CustomModifiers.customHeight
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.ToasterState
import com.simapps.ui_kit.LoadingAnimation

@Composable
fun DeviceScreen(
    toaster: ToasterState,
    state: BluetoothUiState,
    padding: PaddingValues,
    configuration: Configuration,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit,
    onDeviceClick: (BluetoothDevice) -> Unit,
    onDeviceClickPaired: (BluetoothDevice) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize().padding(padding)
    ) {
        Spacer(modifier = Modifier.padding(top = 28.dp))
        BluetoothDeviceList(
            toaster = toaster,
            state = state,
            configuration = configuration,
            onClick = onDeviceClick,
            onClickPaired = onDeviceClickPaired,
            modifier = Modifier
                .fillMaxWidth()
                .padding(3.dp)
        )
        Spacer(modifier = Modifier.padding(top = 12.dp))
        Spacer(modifier = Modifier.weight(1f))
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            Button(
                enabled = !state.isScanning,
                onClick = onStartScan
            ) {
                Text(text = stringResource(id = R.string.startScan))
            }
            Button(
                enabled = state.isScanning,
                onClick = onStopScan
            ) {
                Text(text = stringResource(id = R.string.stopScan))
            }

        }

        Spacer(modifier = Modifier.padding(top = 25.dp))
    }
}


// Add this function to check common printer names
private fun isPrinterByName(name: String?): Boolean {
    if (name == null) return false

    // List of common printer names/keywords
    val printerNames = listOf(
        "Innerprinter",
        "printer",
        "print",
        "thermal",
        "pos",
        "escpos",
        "receipt"
    )

    return printerNames.any { name.lowercase().contains(it) }
}
@Composable
fun ScannedDevice(
    toaster: ToasterState,
    device: BluetoothDevice,
    onClick: (String) -> Unit
) {
    val context = LocalContext.current
    val majDeviceCl: Int = device.bluetoothClass.majorDeviceClass
    val deviceCl: Int = device.bluetoothClass.deviceClass
    OutlinedCard(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                // Check if it's a printer by device class OR by common printer names
                if (majDeviceCl == BluetoothClass.Device.Major.IMAGING &&
                    (deviceCl == 1664 || deviceCl == BluetoothClass.Device.Major.IMAGING) ||
                    isPrinterByName(device.name)
                )
                    onClick(device.address)
                else
                    showToast(
                        context = context,
                        toaster = toaster,
                        message = context.resources.getString(R.string.not_a_printer),
                        type = ToastType.Info,
                    )
            },
        shape = RoundedCornerShape(10.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 4.dp
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(8.dp)
        ) {

                Icon(
                    modifier = Modifier.padding(end = 6.dp),
                    imageVector = getBluetoothDeviceIcon(deviceCl = majDeviceCl),
                    contentDescription = "RSSI Signal"
                )
            Column {
                Text(
                    text = device.name ?: "Unknown Name",
                    style = MaterialTheme.typography.titleMedium
                )


                Text(
                    text = device.address,
                    style = MaterialTheme.typography.bodyMedium
                )

            }
        }
    }

}

@SuppressLint("MissingPermission")
@Composable
fun BluetoothDeviceList(
    toaster: ToasterState,
    state: BluetoothUiState,
    configuration: Configuration,
    onClick: (BluetoothDevice) -> Unit,
    onClickPaired: (BluetoothDevice) -> Unit,
    modifier: Modifier = Modifier
) {


    val pairedDevices = state.pairedDevices
    val scannedDevices = state.scannedDevices

   // val configuration = LocalConfiguration.current
   // val screenHeight = configuration.screenHeightDp.dp / 3 // Divide by 2 for half height

    Text(
        text = stringResource(id = R.string.paired_Devices),
        color = MaterialTheme.colorScheme.error,
        style = MaterialTheme.typography.titleLarge,
        modifier = Modifier.padding(top = 20.dp, start = 16.dp)
    )

    LazyColumn(
        modifier = modifier.customHeight(configuration, 0.35f),
        horizontalAlignment = Alignment.CenterHorizontally,
        contentPadding = PaddingValues(12.dp)
    ) {

        items(
            count = pairedDevices.size,
            key = {
                pairedDevices[it].address
            }
        ) { index ->
              Spacer(modifier = Modifier.padding(top = 12.dp))


            ScannedDevice(
                toaster = toaster,
                device = pairedDevices[index],
                onClick = {
                    onClickPaired(pairedDevices[index])
                }
            )


        }



    }


    Text(
        text = stringResource(id = R.string.scanned_Devices),
        color = MaterialTheme.colorScheme.error,
        style = MaterialTheme.typography.titleLarge,
        modifier = Modifier.padding(start = 16.dp, top = 16.dp)
    )
    LazyColumn(
        modifier = modifier.customHeight(configuration, 0.2f),
        horizontalAlignment = Alignment.CenterHorizontally,
        contentPadding = PaddingValues(12.dp)
    ) {

        if (state.isScanning)
            item {
                Spacer(modifier = Modifier.padding(top = 12.dp))
                LoadingAnimation()
            }

        items(scannedDevices) { device ->
            Spacer(modifier = Modifier.padding(top = 12.dp))
            ScannedDevice(
                toaster = toaster,
                device = device,
                onClick = {
                    onClick(device)
                }
            )


        }
    }
}

