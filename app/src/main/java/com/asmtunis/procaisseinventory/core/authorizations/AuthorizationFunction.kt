package com.asmtunis.procaisseinventory.core.authorizations

import com.asmtunis.procaisseinventory.auth.login.data.domaine.Authorization
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse

object AuthorizationFunction {
    fun haveAuth(value: String): Boolean = value == "1"

    fun haveDiscountAuth(proCaisseAuthorization: List<Authorization>): Boolean =
                (
                proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.MODIFY_PRICE} ||
                proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.FREE_DISCOUNT} ||
                proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.ART_REM_DISCOUNT } ||
                (proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT}
                 && proCaisseAuthorization.firstOrNull { it.AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT}?.AutValues != null )
                )
                && !proCaisseAuthorization.any { it.AutoCodeAu == AuthorizationValuesProCaisse.NO_DISCOUNT}




     fun addToAuthList(result: List<Authorization>): List<Authorization> {
         val proInventoryAuthorizationList = result.filter { authorisation ->
                authorisation.AutEtat == "1" //&& authorisation.AutoTypeMenu == Globals.PRO_INVENTORY_AUTHORIZATION_TYPE_MENU
            }

        return   proInventoryAuthorizationList//.map { it.AutoCodeAu }
        }



}