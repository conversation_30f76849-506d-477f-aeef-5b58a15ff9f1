package com.asmtunis.procaisseinventory.core.print

import kotlinx.serialization.Serializable

@Serializable
data class PrintingData(
    val printIcon: Boolean = true,
    val printCompanyName: Boolean = true,
    val taxeArticle: Boolean = true,
    val printClientSold: <PERSON>olean = true,
    val printAppVersion: Boolean = true,
    val printCachet: Boolean = true,
    val printViaWifi: Boolean = false,
    val useSunmiPrinter: Boolean = false,
    val paperSize: Int = 80, // Default to 80mm paper
    val enableNotes: Boolean = false, // Enable notes field after client address
    val noteText: String = "" // The note text to be printed
)
