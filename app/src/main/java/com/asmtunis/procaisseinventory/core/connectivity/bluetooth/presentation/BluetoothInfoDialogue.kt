package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation

import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.BluetoothStatus
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim

@Composable
fun BluetoothInfoDialogue(
    printResult: String,
    onOpenPrintInfoDialogueChange:(Boolean)->Unit
) {

    AlertDialog(
        onDismissRequest = {
            // Dismiss the dialog when the user clicks outside the dialog or on the back
            // button. If you want to disable that functionality, simply use an empty
            // onDismissRequest.
            onOpenPrintInfoDialogueChange(false)
        },
        title = {
            if(printResult == BluetoothStatus.IS_PRINTING.status)
            Text(text = stringResource(R.string.printing))
        },
        text = {
            if(printResult != BluetoothStatus.IS_PRINTING.status)
            Text(text = printResult)
        },
        icon = {
            // if(openPrintInfoDialogue) {
            if(printResult== BluetoothStatus.IS_PRINTING.status)
                LottieAnim(lotti = R.raw.is_printing, size = 50.dp)
            else if (printResult != BluetoothStatus.IS_PRINTING.status &&
                printResult != BluetoothStatus.PRINT_SUCCESS.status &&
                printResult != BluetoothStatus.EMPTY_PAIRED_LIST.status  )
                LottieAnim(
                    lotti = R.raw.print_error,
                    size = 50.dp)
            // }
        },
        confirmButton = {
            /* TextButton(
                 onClick = {
                     printViewModel.onOpenPrintInfoDialogueChange(false)
                 }
             ) {
                 Text("Confirm")
             }*/
        },
        dismissButton = {
            TextButton(
                onClick = {
                    onOpenPrintInfoDialogueChange(false)
                }
            ) {
                Text(stringResource(R.string.OK))
            }
        }
    )
}