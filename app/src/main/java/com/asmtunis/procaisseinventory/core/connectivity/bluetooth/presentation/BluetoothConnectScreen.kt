package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation

import android.Manifest
import android.os.Build
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.presentation.components.DeviceScreen
import com.asmtunis.procaisseinventory.core.print.bluetooth.PrintViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.AskPermission
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState
import com.google.accompanist.permissions.ExperimentalPermissionsApi

@OptIn(ExperimentalPermissionsApi::class)
@Composable
fun BluetoothConnectScreen(
    navigate: (route: String) -> Unit,
    navigateUp: () -> Unit,
    settingViewModel: SettingViewModel,
    printViewModel: PrintViewModel,
    bluetoothVM: BluetoothViewModel,
    navigationDrawerViewModel: NavigationDrawerViewModel,
) {
    val context = LocalContext.current

    val toaster = rememberToasterState()
    ToastKMM(toaster = toaster, darkTheme = settingViewModel.isDarkTheme)


    val configuration = LocalConfiguration.current

    val bluetoothState by bluetoothVM.state.collectAsState()

    LaunchedEffect(key1 = bluetoothState) {
        if(bluetoothState.isConnected) {
            showToast(
                context = context,
                toaster = toaster,
                message = context.resources.getString(R.string.bluetooth_connected),
                type =  ToastType.Info,
            )


            printViewModel.onproccedPrintingChange(true)
           navigateUp()
        }
        else {
            bluetoothState.errorMessage?.let { message ->

                showToast(
                    context = context,
                    toaster = toaster,
                    message = message,
                    type =  ToastType.Info,
                )
            }
        }
    }




    Scaffold() { padding ->
        when {
            bluetoothState.isConnecting -> {
                Column(
                    modifier = Modifier.fillMaxSize().padding(padding),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    CircularProgressIndicator()
                    Spacer(modifier = Modifier.height(12.dp))
                    Text(text = stringResource(R.string.connecting_bluetooth))
                }
            }
            else -> {
              val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                  listOf(
                            Manifest.permission.BLUETOOTH_SCAN,
                            Manifest.permission.BLUETOOTH_CONNECT,
                            Manifest.permission.ACCESS_COARSE_LOCATION,
                            Manifest.permission.ACCESS_FINE_LOCATION,
                        )
                }
                else {
                    if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {

                        listOf(
                                Manifest.permission.BLUETOOTH_SCAN,
                                Manifest.permission.BLUETOOTH_CONNECT,
                                Manifest.permission.ACCESS_COARSE_LOCATION,
                                Manifest.permission.ACCESS_FINE_LOCATION
                            )

                    }
                    else
                        listOf(
                            Manifest.permission.ACCESS_COARSE_LOCATION,
                            Manifest.permission.ACCESS_FINE_LOCATION,
                        )

                }

                AskPermission(
                    permission = permission,
                    permissionNotAvailableContent = { permissionState ->
                        val scrollState = rememberScrollState()
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .verticalScroll(scrollState)
                                .padding(padding),
                            verticalArrangement = Arrangement.Center,
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            LottieAnim(lotti = R.raw.emptystate)
                            Spacer(modifier = Modifier.height(16.dp))

                            val textToShow = if (permissionState.shouldShowRationale) {
                                stringResource(R.string.access_bluetooth_request_permession)
                            } else {
                                stringResource(R.string.bluetooth_not_available)
                            }
                            Spacer(modifier = Modifier.height(16.dp))
                            Text(textToShow)
                            Spacer(modifier = Modifier.height(8.dp))
                            Button(onClick = { permissionState.launchMultiplePermissionRequest() }) {
                                Text(stringResource(R.string.request_bluetooth_auth))
                            }
                        }
                    },
                    content = {
                        DeviceScreen(
                            toaster = toaster,
                            state = bluetoothState,
                            padding = padding,
                            configuration = configuration,
                            onStartScan = bluetoothVM::startScan,
                            onStopScan = bluetoothVM::stopScan,
                            onDeviceClick = {
                                bluetoothVM.connectToDevice(it)
                                printViewModel.onDeviceAddressChange(adress = it.address)
                                           },
                            onDeviceClickPaired = {
                                printViewModel.onDeviceAddressChange(adress = it.address)

                                printViewModel.onproccedPrintingChange(true)
                                navigateUp()


                            }
                        )
                    }
                )

            }
        }

    }


    // BleApp()

}