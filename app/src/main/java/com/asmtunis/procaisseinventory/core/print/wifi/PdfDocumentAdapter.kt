package com.asmtunis.procaisseinventory.core.print.wifi

import android.content.Context
import android.os.Bundle
import android.os.CancellationSignal
import android.os.ParcelFileDescriptor
import android.print.PageRange
import android.print.PrintAttributes
import android.print.PrintDocumentAdapter
import android.print.PrintDocumentInfo
import android.util.Log
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream


class PdfDocumentAdapter(var context: Context, var path: String,var action: () -> Unit) :
    PrintDocumentAdapter() {

    override fun onLayout(
        oldAttributes: PrintAttributes,
        printAttributes1: PrintAttributes,
        cancellationSignal: CancellationSignal,
        layoutResultCallback: LayoutResultCallback,
        extras: Bundle
    ) {
        if (cancellationSignal.isCanceled) layoutResultCallback.onLayoutCancelled() else {
            val builder = PrintDocumentInfo.Builder("file name")
            builder.setContentType(PrintDocumentInfo.CONTENT_TYPE_DOCUMENT)
                .setPageCount(PrintDocumentInfo.PAGE_COUNT_UNKNOWN)
                .build()
            layoutResultCallback.onLayoutFinished(
                builder.build(),
                printAttributes1 != printAttributes1
            )
        }
    }

    override fun onWrite(
        pages: Array<PageRange>,
        parcelFileDescriptor: ParcelFileDescriptor,
        cancellationSignal: CancellationSignal,
        writeResultCallback: WriteResultCallback
    ) {
        var inputStream: InputStream? = null
        var outputStream: OutputStream? = null
        try {
            val file = File(path)
            inputStream = FileInputStream(file)
            outputStream = FileOutputStream(parcelFileDescriptor.fileDescriptor)
            val buff = ByteArray(16384)
            var size: Int
            while (inputStream.read(buff).also { size = it } >= 0 && !cancellationSignal.isCanceled) {
                outputStream.write(buff, 0, size)
            }
            if (cancellationSignal.isCanceled) writeResultCallback.onWriteCancelled() else {
                writeResultCallback.onWriteFinished(arrayOf(PageRange.ALL_PAGES))
            }
        } catch (e: Exception) {
            writeResultCallback.onWriteFailed(e.message)
            Log.e("Harshita", e.message!!)
            e.printStackTrace()
        } finally {
            try {

               if(inputStream!=null) inputStream.close()
                if(outputStream!=null)  outputStream.close()
            } catch (ex: IOException) {
                Log.e("Harshita", "" + ex.message)
            }
        }
    }

    override fun onFinish() {
        super.onFinish()
        action()
    }
}
