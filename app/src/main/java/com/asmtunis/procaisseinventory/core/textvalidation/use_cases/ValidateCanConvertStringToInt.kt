package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult


class ValidateCanConvertStringToInt {
    fun execute(value: String): ValidationResult {
        if (value.toIntOrNull() == null) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.il_faut_entre_un_nombre)
            )
        }
        return ValidationResult(
            successful = true
        )
    }
}