package com.asmtunis.procaisseinventory.core.model

import androidx.room.ColumnInfo
import com.asmtunis.procaisseinventory.core.enum_classes.ItemStatus
import kotlinx.serialization.Transient


open class BaseModel {
    @ColumnInfo(name = "Status")
  //  @SerialName("Status")
    //   @Exclude
    @Transient
    var status: String = ItemStatus.SELECTED.status

    @ColumnInfo(name = "IsSync")
   // @SerialName("IsSync")
    // @Exclude
    @Transient
    var isSync: Boolean = true
}






