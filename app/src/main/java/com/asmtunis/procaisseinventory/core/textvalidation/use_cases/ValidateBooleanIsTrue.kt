package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult


class ValidateBooleanIsTrue {
    fun execute(value: Boolean): ValidationResult {
        if (!value) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.select)
            )
        }
        return ValidationResult(
            successful = true
        )
    }
}