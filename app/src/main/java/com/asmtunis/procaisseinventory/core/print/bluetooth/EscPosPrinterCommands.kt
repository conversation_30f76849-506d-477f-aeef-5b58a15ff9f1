package com.asmtunis.procaisseinventory.core.print.bluetooth



import android.graphics.Bitmap
import androidx.core.graphics.get
import androidx.core.graphics.scale
import kotlin.math.ceil


object EscPosPrinterCommands {
    const val LF: Byte = 0x0A
    val RESET_PRINTER = byteArrayOf(0x1B, 0x40)
    val TEXT_ALIGN_LEFT = byteArrayOf(0x1B, 0x61, 0x00)
    val TEXT_ALIGN_CENTER = byteArrayOf(0x1B, 0x61, 0x01)
    val TEXT_ALIGN_RIGHT = byteArrayOf(0x1B, 0x61, 0x02)
    val TEXT_WEIGHT_NORMAL = byteArrayOf(0x1B, 0x45, 0x00)
    val TEXT_WEIGHT_BOLD = byteArrayOf(0x1B, 0x45, 0x01)
    val TEXT_FONT_A = byteArrayOf(0x1B, 0x4D, 0x00)
    val TEXT_FONT_B = byteArrayOf(0x1B, 0x4D, 0x01)
    val TEXT_FONT_C = byteArrayOf(0x1B, 0x4D, 0x02)
    val TEXT_FONT_D = byteArrayOf(0x1B, 0x4D, 0x03)
    val TEXT_FONT_E = byteArrayOf(0x1B, 0x4D, 0x04)
    val TEXT_SIZE_NORMAL = byteArrayOf(0x1D, 0x21, 0x00)
    val TEXT_SIZE_DOUBLE_HEIGHT = byteArrayOf(0x1D, 0x21, 0x01)
    val TEXT_SIZE_DOUBLE_WIDTH = byteArrayOf(0x1D, 0x21, 0x10)
    val TEXT_SIZE_BIG = byteArrayOf(0x1D, 0x21, 0x11)
    val TEXT_UNDERLINE_OFF = byteArrayOf(0x1B, 0x2D, 0x00)
    val TEXT_UNDERLINE_ON = byteArrayOf(0x1B, 0x2D, 0x01)
    val TEXT_UNDERLINE_LARGE = byteArrayOf(0x1B, 0x2D, 0x02)
    val TEXT_DOUBLE_STRIKE_OFF = byteArrayOf(0x1B, 0x47, 0x00)
    val TEXT_DOUBLE_STRIKE_ON = byteArrayOf(0x1B, 0x47, 0x01)
    val TEXT_COLOR_BLACK = byteArrayOf(0x1B, 0x72, 0x00)
    val TEXT_COLOR_RED = byteArrayOf(0x1B, 0x72, 0x01)
    val TEXT_COLOR_REVERSE_OFF = byteArrayOf(0x1D, 0x42, 0x00)
    val TEXT_COLOR_REVERSE_ON = byteArrayOf(0x1D, 0x42, 0x01)

    private fun initImageCommand(bytesByLine: Int, bitmapHeight: Int): ByteArray {
        val xH = bytesByLine / 256
        val xL = bytesByLine - xH * 256
        val yH = bitmapHeight / 256
        val yL = bitmapHeight - yH * 256
        val imageBytes = ByteArray(8 + bytesByLine * bitmapHeight)
        System.arraycopy(
            byteArrayOf(
                0x1D,
                0x76,
                0x30,
                0x00,
                xL.toByte(),
                xH.toByte(),
                yL.toByte(),
                yH.toByte()
            ), 0, imageBytes, 0, 8
        )
        return imageBytes
    }

    /**
     * Convert Bitmap instance to a byte array compatible with ESC/POS printer.
     *
     * @param bitmap Bitmap to be convert
     * @return Bytes contain the image in ESC/POS command
     */
    fun bitmapToBytes(bitmap: Bitmap, height:Int= 100): ByteArray {


       val ratio:Int = bitmap.height/height

         val command = if(height == bitmap.height) bitmap
             else bitmap.scale(bitmap.width / ratio, height, false)

        val bitmapWidth = command.width
        val bitmapHeight = command.height


        val bytesByLine = ceil((bitmapWidth.toFloat() / 8f).toDouble()).toInt()


        val imageBytes = initImageCommand(bytesByLine, bitmapHeight)
        var i = 8
        for (posY in 0 until bitmapHeight) {
            var j = 0
            while (j < bitmapWidth) {
                val stringBinary = StringBuilder()
                for (k in 0..7) {
                    val posX = j + k
                    if (posX < bitmapWidth) {
                       val color = command[posX, posY]
                     //  val color = bitmap.getInt(posX, posY)
                       // val color = bitmap.readPixelsUnsafe(posX, posY,bitmapWidth,bitmapHeight,IntArray(9),9)
                        val r = color shr 16 and 0xff
                        val g = color shr 8 and 0xff
                        val b = color and 0xff
                        if (r > 160 && g > 160 && b > 160) {
                            stringBinary.append("0")
                        } else {
                            stringBinary.append("1")
                        }
                    } else {
                        stringBinary.append("0")
                    }
                }
                imageBytes[i++] = stringBinary.toString().toInt(2).toByte()
                j += 8
            }
        }
        return imageBytes
    }
}
