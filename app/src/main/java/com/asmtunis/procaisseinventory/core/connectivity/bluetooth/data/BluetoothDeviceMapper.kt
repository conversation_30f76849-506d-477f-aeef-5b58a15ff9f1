package com.asmtunis.procaisseinventory.core.connectivity.bluetooth.data

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import com.asmtunis.procaisseinventory.core.connectivity.bluetooth.domain.bt.BluetoothDeviceDomain

@SuppressLint("MissingPermission")
fun BluetoothDevice.toBluetoothDeviceDomain(): BluetoothDeviceDomain {
    return BluetoothDeviceDomain(
        name = name,
        address = address,
        bluetoothClass = bluetoothClass,
        bondState = bondState,
        type = type,
    )
}


