package com.asmtunis.procaisseinventory.core.textvalidation.use_cases

import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.articles.data.article.domaine.Article
import com.asmtunis.procaisseinventory.core.UiText
import com.asmtunis.procaisseinventory.core.textvalidation.domain.ValidationResult
import com.asmtunis.procaisseinventory.core.utils.StringUtils.stringToDouble


class ValidateArticleQuantity {

    fun execute(inputQuantity: String, article : Article): ValidationResult {
        if (inputQuantity.isBlank()) {
            return ValidationResult(
                successful = false,
                errorMessage = UiText.StringResource(resId = R.string.cant_be_empty)
            )
        }else if(stringToDouble(inputQuantity) > stringToDouble(article.aRTQteStock)) {
                return ValidationResult(
                    successful = false,
                    errorMessage = UiText.StringResource(resId = R.string.qte_in_stock, article.aRTQteStock, article.uNITEARTICLECodeUnite?:"")
                )
            } else if (stringToDouble(inputQuantity) <= 0) {
                return ValidationResult(
                    successful = false,
                    errorMessage = UiText.StringResource(resId = R.string.qte_more_than_zero)
                )
            }

        return ValidationResult(
            successful = true
        )
    }
}