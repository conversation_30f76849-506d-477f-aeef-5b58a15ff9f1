package com.asmtunis.procaisseinventory.setting

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.twotone.SwitchAccessShortcut
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedCard
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.material3.windowsizeclass.WindowWidthSizeClass
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.base_config.data.domaine.BaseConfig
import com.asmtunis.procaisseinventory.core.Globals
import com.asmtunis.procaisseinventory.core.Globals.OPERATEUR_PATRIMOINE
import com.asmtunis.procaisseinventory.core.authorizations.authorizationvalues.AuthorizationValuesProCaisse
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.sync_workmanager.SyncWorkerViewModel
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.setting.adaptive_view.SettingColumnView
import com.asmtunis.procaisseinventory.setting.adaptive_view.SettingRowView
import com.asmtunis.procaisseinventory.shared_ui_components.AppBar
import com.asmtunis.procaisseinventory.shared_ui_components.LottieAnim
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.simapps.ui_kit.custom_cards.ItemDetail
import kotlinx.coroutines.launch


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingScreen(
    navigate: (route: Any) -> Unit,
    popBackStack: () -> Unit,
    settingVM: SettingViewModel,
    mainViewModel: MainViewModel,
    navDrawerViewModel: NavigationDrawerViewModel,
    syncWorkerVM: SyncWorkerViewModel,
    dataVm: DataViewModel,
    networkViewModel: NetworkViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel
) {
    val context = LocalContext.current
    val selectedBaseconfig: BaseConfig = dataVm.selectedBaseConfig

    val uiWindowState = settingVM.uiWindowState
    val windowSize = uiWindowState.windowSize!!

    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    
    val printParams = dataVm.printData

    val authorisationList = getProCaisseDataViewModel.authorizationList// + getProInventoryDataViewModel.authorizationList

    val licenceProCaisse = selectedBaseconfig.licences.firstOrNull { it.produit == Globals.PRO_CAISSE_MOBILITY }
    val licenceProInventory = selectedBaseconfig.licences.firstOrNull { it.produit == Globals.PRO_INVENTORY }


    val isDemo = selectedBaseconfig.designation_base== Globals.DEMO_BASE_CONFIG

    val utilisateur = mainViewModel.utilisateur
    val isOperateurPatrimoine = utilisateur.typeUser == OPERATEUR_PATRIMOINE

    Scaffold(
        topBar = {
            AppBar(
                baseConfig = selectedBaseconfig,
                isConnected = networkViewModel.isConnected,
                onNavigationClick = { popBackStack() },
                navIcon = Icons.AutoMirrored.Filled.ArrowBack,
                title = stringResource(id = R.string.settings),
            )
        }
    ) { padding ->

        if(settingVM.showAuthorisationList) {
            ModalBottomSheet(
                sheetState = sheetState,
                onDismissRequest = {
                    scope.launch { sheetState.hide() }
                    settingVM.onShowAuthorisationListChange(false)
                },
            ){
                LazyColumn(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    contentPadding = PaddingValues(12.dp)
                ) {


                    if(authorisationList.isEmpty()) {

                        item {
                            Text(
                                modifier = Modifier.fillMaxWidth(),
                                text = stringResource(id= R.string.no_authorisation),
                                color = MaterialTheme.colorScheme.error,
                                textAlign = TextAlign.Center,
                                style = MaterialTheme.typography.titleLarge
                            )
                        }

                        item {
                            LottieAnim(lotti = R.raw.emptystate, size = 150.dp)
                        }
                    }
                    items(
                        count = authorisationList.size,
                         key = { authorisationList[it].AutoCode }
                    ) { index ->
                        Spacer(modifier = Modifier.padding(top = 12.dp))

                       val fixDiscount = if(authorisationList[index].AutoCodeAu == AuthorizationValuesProCaisse.FIXED_DISCOUNT) if(authorisationList[index].AutValues != null) authorisationList[index].AutValues + " %" else " (Il faut définir la valeur du remise fixe)" else ""

                        OutlinedCard(
                            modifier = Modifier.fillMaxWidth(),
                            shape = RoundedCornerShape(10.dp),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = 4.dp
                            )
                        ) {
                            ItemDetail(
                                modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                                title = (authorisationList[index].AutoDescription?:"") +" "+ fixDiscount,
                                dataText = authorisationList[index].AutoTypeMenu?:"",
                                icon = Icons.TwoTone.SwitchAccessShortcut
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(50.dp))

            }
        }



        when (windowSize.widthSizeClass) {
            WindowWidthSizeClass.Compact -> {
                SettingColumnView(
                navigate = { navigate(it) },
                padding = padding,
                selectedBaseconfig = selectedBaseconfig,
                licenceProCaisse = licenceProCaisse,
                licenceProInventory = licenceProInventory,
                utilisateur = utilisateur,
                isOperateurPatrimoine = isOperateurPatrimoine,
                printParams = printParams,
                dataVm = dataVm,
                settingVM = settingVM,
                isDemo = isDemo,
                )
            }
            WindowWidthSizeClass.Expanded,
            WindowWidthSizeClass.Medium -> {
                SettingRowView(
                    navigate = { navigate(it) },
                    padding = padding,
                    selectedBaseconfig = selectedBaseconfig,
                    licenceProCaisse = licenceProCaisse,
                    licenceProInventory = licenceProInventory,
                    utilisateur = utilisateur,
                    isOperateurPatrimoine = isOperateurPatrimoine,
                    printParams = printParams,
                    dataVm = dataVm,
                    settingVM = settingVM,
                    isDemo = isDemo,
                )
            }
            else -> {
                SettingColumnView(
                    navigate = { navigate(it) },
                    padding = padding,
                    selectedBaseconfig = selectedBaseconfig,
                    licenceProCaisse = licenceProCaisse,
                    licenceProInventory = licenceProInventory,
                    utilisateur = utilisateur,
                    isOperateurPatrimoine = isOperateurPatrimoine,
                    printParams = printParams,
                    dataVm = dataVm,
                    settingVM = settingVM,
                    isDemo = isDemo,
                )
            }
        }
    }

}






